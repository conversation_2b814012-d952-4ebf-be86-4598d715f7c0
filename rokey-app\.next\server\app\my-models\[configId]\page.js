(()=>{var e={};e.id=4150,e.ids=[4150],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx","default")},23051:(e,t,r)=>{Promise.resolve().then(r.bind(r,42384))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},42384:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>no});var a,n,s,o=r(60687),i=r(43210),l=r.t(i,2),d=r(16189),c=r(66368);let u=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],m=e=>u.find(t=>t.id===e),f=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var p=r(71031),h=r(51426),g=r(26403),x=r(44725);let y=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))});var v=r(58089),b=r(97450);let w=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))});var j=r(57891);let N=i.forwardRef(function({title:e,titleId:t,...r},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?i.createElement("title",{id:t},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))});var k=r(50942),_=r(71178);let E=Math.min,C=Math.max,S=Math.round,A=Math.floor,R=e=>({x:e,y:e}),P={left:"right",right:"left",bottom:"top",top:"bottom"},T={start:"end",end:"start"};function M(e,t){return"function"==typeof e?e(t):e}function O(e){return e.split("-")[0]}function I(e){return e.split("-")[1]}function D(e){return"x"===e?"y":"x"}function L(e){return"y"===e?"height":"width"}function $(e){return["top","bottom"].includes(O(e))?"y":"x"}function F(e){return e.replace(/start|end/g,e=>T[e])}function z(e){return e.replace(/left|right|bottom|top/g,e=>P[e])}function W(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function K(e){let{x:t,y:r,width:a,height:n}=e;return{width:a,height:n,top:r,left:t,right:t+a,bottom:r+n,x:t,y:r}}function B(e,t,r){let a,{reference:n,floating:s}=e,o=$(t),i=D($(t)),l=L(i),d=O(t),c="y"===o,u=n.x+n.width/2-s.width/2,m=n.y+n.height/2-s.height/2,f=n[l]/2-s[l]/2;switch(d){case"top":a={x:u,y:n.y-s.height};break;case"bottom":a={x:u,y:n.y+n.height};break;case"right":a={x:n.x+n.width,y:m};break;case"left":a={x:n.x-s.width,y:m};break;default:a={x:n.x,y:n.y}}switch(I(t)){case"start":a[i]-=f*(r&&c?-1:1);break;case"end":a[i]+=f*(r&&c?-1:1)}return a}let q=async(e,t,r)=>{let{placement:a="bottom",strategy:n="absolute",middleware:s=[],platform:o}=r,i=s.filter(Boolean),l=await (null==o.isRTL?void 0:o.isRTL(t)),d=await o.getElementRects({reference:e,floating:t,strategy:n}),{x:c,y:u}=B(d,a,l),m=a,f={},p=0;for(let r=0;r<i.length;r++){let{name:s,fn:h}=i[r],{x:g,y:x,data:y,reset:v}=await h({x:c,y:u,initialPlacement:a,placement:m,strategy:n,middlewareData:f,rects:d,platform:o,elements:{reference:e,floating:t}});c=null!=g?g:c,u=null!=x?x:u,f={...f,[s]:{...f[s],...y}},v&&p<=50&&(p++,"object"==typeof v&&(v.placement&&(m=v.placement),v.rects&&(d=!0===v.rects?await o.getElementRects({reference:e,floating:t,strategy:n}):v.rects),{x:c,y:u}=B(d,m,l)),r=-1)}return{x:c,y:u,placement:m,strategy:n,middlewareData:f}};async function H(e,t){var r;void 0===t&&(t={});let{x:a,y:n,platform:s,rects:o,elements:i,strategy:l}=e,{boundary:d="clippingAncestors",rootBoundary:c="viewport",elementContext:u="floating",altBoundary:m=!1,padding:f=0}=M(t,e),p=W(f),h=i[m?"floating"===u?"reference":"floating":u],g=K(await s.getClippingRect({element:null==(r=await (null==s.isElement?void 0:s.isElement(h)))||r?h:h.contextElement||await (null==s.getDocumentElement?void 0:s.getDocumentElement(i.floating)),boundary:d,rootBoundary:c,strategy:l})),x="floating"===u?{x:a,y:n,width:o.floating.width,height:o.floating.height}:o.reference,y=await (null==s.getOffsetParent?void 0:s.getOffsetParent(i.floating)),v=await (null==s.isElement?void 0:s.isElement(y))&&await (null==s.getScale?void 0:s.getScale(y))||{x:1,y:1},b=K(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:x,offsetParent:y,strategy:l}):x);return{top:(g.top-b.top+p.top)/v.y,bottom:(b.bottom-g.bottom+p.bottom)/v.y,left:(g.left-b.left+p.left)/v.x,right:(b.right-g.right+p.right)/v.x}}async function U(e,t){let{placement:r,platform:a,elements:n}=e,s=await (null==a.isRTL?void 0:a.isRTL(n.floating)),o=O(r),i=I(r),l="y"===$(r),d=["left","top"].includes(o)?-1:1,c=s&&l?-1:1,u=M(t,e),{mainAxis:m,crossAxis:f,alignmentAxis:p}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return i&&"number"==typeof p&&(f="end"===i?-1*p:p),l?{x:f*c,y:m*d}:{x:m*d,y:f*c}}function V(){return"undefined"!=typeof window}function Y(e){return J(e)?(e.nodeName||"").toLowerCase():"#document"}function X(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function G(e){var t;return null==(t=(J(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function J(e){return!!V()&&(e instanceof Node||e instanceof X(e).Node)}function Z(e){return!!V()&&(e instanceof Element||e instanceof X(e).Element)}function Q(e){return!!V()&&(e instanceof HTMLElement||e instanceof X(e).HTMLElement)}function ee(e){return!!V()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof X(e).ShadowRoot)}function et(e){let{overflow:t,overflowX:r,overflowY:a,display:n}=eo(e);return/auto|scroll|overlay|hidden|clip/.test(t+a+r)&&!["inline","contents"].includes(n)}function er(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function ea(e){let t=en(),r=Z(e)?eo(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function en(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function es(e){return["html","body","#document"].includes(Y(e))}function eo(e){return X(e).getComputedStyle(e)}function ei(e){return Z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function el(e){if("html"===Y(e))return e;let t=e.assignedSlot||e.parentNode||ee(e)&&e.host||G(e);return ee(t)?t.host:t}function ed(e,t,r){var a;void 0===t&&(t=[]),void 0===r&&(r=!0);let n=function e(t){let r=el(t);return es(r)?t.ownerDocument?t.ownerDocument.body:t.body:Q(r)&&et(r)?r:e(r)}(e),s=n===(null==(a=e.ownerDocument)?void 0:a.body),o=X(n);if(s){let e=ec(o);return t.concat(o,o.visualViewport||[],et(n)?n:[],e&&r?ed(e):[])}return t.concat(n,ed(n,[],r))}function ec(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eu(e){let t=eo(e),r=parseFloat(t.width)||0,a=parseFloat(t.height)||0,n=Q(e),s=n?e.offsetWidth:r,o=n?e.offsetHeight:a,i=S(r)!==s||S(a)!==o;return i&&(r=s,a=o),{width:r,height:a,$:i}}function em(e){return Z(e)?e:e.contextElement}function ef(e){let t=em(e);if(!Q(t))return R(1);let r=t.getBoundingClientRect(),{width:a,height:n,$:s}=eu(t),o=(s?S(r.width):r.width)/a,i=(s?S(r.height):r.height)/n;return o&&Number.isFinite(o)||(o=1),i&&Number.isFinite(i)||(i=1),{x:o,y:i}}let ep=R(0);function eh(e){let t=X(e);return en()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ep}function eg(e,t,r,a){var n;void 0===t&&(t=!1),void 0===r&&(r=!1);let s=e.getBoundingClientRect(),o=em(e),i=R(1);t&&(a?Z(a)&&(i=ef(a)):i=ef(e));let l=(void 0===(n=r)&&(n=!1),a&&(!n||a===X(o))&&n)?eh(o):R(0),d=(s.left+l.x)/i.x,c=(s.top+l.y)/i.y,u=s.width/i.x,m=s.height/i.y;if(o){let e=X(o),t=a&&Z(a)?X(a):a,r=e,n=ec(r);for(;n&&a&&t!==r;){let e=ef(n),t=n.getBoundingClientRect(),a=eo(n),s=t.left+(n.clientLeft+parseFloat(a.paddingLeft))*e.x,o=t.top+(n.clientTop+parseFloat(a.paddingTop))*e.y;d*=e.x,c*=e.y,u*=e.x,m*=e.y,d+=s,c+=o,n=ec(r=X(n))}}return K({width:u,height:m,x:d,y:c})}function ex(e,t){let r=ei(e).scrollLeft;return t?t.left+r:eg(G(e)).left+r}function ey(e,t,r){void 0===r&&(r=!1);let a=e.getBoundingClientRect();return{x:a.left+t.scrollLeft-(r?0:ex(e,a)),y:a.top+t.scrollTop}}function ev(e,t,r){let a;if("viewport"===t)a=function(e,t){let r=X(e),a=G(e),n=r.visualViewport,s=a.clientWidth,o=a.clientHeight,i=0,l=0;if(n){s=n.width,o=n.height;let e=en();(!e||e&&"fixed"===t)&&(i=n.offsetLeft,l=n.offsetTop)}return{width:s,height:o,x:i,y:l}}(e,r);else if("document"===t)a=function(e){let t=G(e),r=ei(e),a=e.ownerDocument.body,n=C(t.scrollWidth,t.clientWidth,a.scrollWidth,a.clientWidth),s=C(t.scrollHeight,t.clientHeight,a.scrollHeight,a.clientHeight),o=-r.scrollLeft+ex(e),i=-r.scrollTop;return"rtl"===eo(a).direction&&(o+=C(t.clientWidth,a.clientWidth)-n),{width:n,height:s,x:o,y:i}}(G(e));else if(Z(t))a=function(e,t){let r=eg(e,!0,"fixed"===t),a=r.top+e.clientTop,n=r.left+e.clientLeft,s=Q(e)?ef(e):R(1),o=e.clientWidth*s.x,i=e.clientHeight*s.y;return{width:o,height:i,x:n*s.x,y:a*s.y}}(t,r);else{let r=eh(e);a={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return K(a)}function eb(e){return"static"===eo(e).position}function ew(e,t){if(!Q(e)||"fixed"===eo(e).position)return null;if(t)return t(e);let r=e.offsetParent;return G(e)===r&&(r=r.ownerDocument.body),r}function ej(e,t){let r=X(e);if(er(e))return r;if(!Q(e)){let t=el(e);for(;t&&!es(t);){if(Z(t)&&!eb(t))return t;t=el(t)}return r}let a=ew(e,t);for(;a&&["table","td","th"].includes(Y(a))&&eb(a);)a=ew(a,t);return a&&es(a)&&eb(a)&&!ea(a)?r:a||function(e){let t=el(e);for(;Q(t)&&!es(t);){if(ea(t))return t;if(er(t))break;t=el(t)}return null}(e)||r}let eN=async function(e){let t=this.getOffsetParent||ej,r=this.getDimensions,a=await r(e.floating);return{reference:function(e,t,r){let a=Q(t),n=G(t),s="fixed"===r,o=eg(e,!0,s,t),i={scrollLeft:0,scrollTop:0},l=R(0);if(a||!a&&!s)if(("body"!==Y(t)||et(n))&&(i=ei(t)),a){let e=eg(t,!0,s,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else n&&(l.x=ex(n));s&&!a&&n&&(l.x=ex(n));let d=!n||a||s?R(0):ey(n,i);return{x:o.left+i.scrollLeft-l.x-d.x,y:o.top+i.scrollTop-l.y-d.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}},ek={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:a,strategy:n}=e,s="fixed"===n,o=G(a),i=!!t&&er(t.floating);if(a===o||i&&s)return r;let l={scrollLeft:0,scrollTop:0},d=R(1),c=R(0),u=Q(a);if((u||!u&&!s)&&(("body"!==Y(a)||et(o))&&(l=ei(a)),Q(a))){let e=eg(a);d=ef(a),c.x=e.x+a.clientLeft,c.y=e.y+a.clientTop}let m=!o||u||s?R(0):ey(o,l,!0);return{width:r.width*d.x,height:r.height*d.y,x:r.x*d.x-l.scrollLeft*d.x+c.x+m.x,y:r.y*d.y-l.scrollTop*d.y+c.y+m.y}},getDocumentElement:G,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:a,strategy:n}=e,s=[..."clippingAncestors"===r?er(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let a=ed(e,[],!1).filter(e=>Z(e)&&"body"!==Y(e)),n=null,s="fixed"===eo(e).position,o=s?el(e):e;for(;Z(o)&&!es(o);){let t=eo(o),r=ea(o);r||"fixed"!==t.position||(n=null),(s?!r&&!n:!r&&"static"===t.position&&!!n&&["absolute","fixed"].includes(n.position)||et(o)&&!r&&function e(t,r){let a=el(t);return!(a===r||!Z(a)||es(a))&&("fixed"===eo(a).position||e(a,r))}(e,o))?a=a.filter(e=>e!==o):n=t,o=el(o)}return t.set(e,a),a}(t,this._c):[].concat(r),a],o=s[0],i=s.reduce((e,r)=>{let a=ev(t,r,n);return e.top=C(a.top,e.top),e.right=E(a.right,e.right),e.bottom=E(a.bottom,e.bottom),e.left=C(a.left,e.left),e},ev(t,o,n));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:ej,getElementRects:eN,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=eu(e);return{width:t,height:r}},getScale:ef,isElement:Z,isRTL:function(e){return"rtl"===eo(e).direction}};function e_(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eE=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,a;let{x:n,y:s,placement:o,middlewareData:i}=t,l=await U(t,e);return o===(null==(r=i.offset)?void 0:r.placement)&&null!=(a=i.arrow)&&a.alignmentOffset?{}:{x:n+l.x,y:s+l.y,data:{...l,placement:o}}}}},eC=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:a,placement:n}=t,{mainAxis:s=!0,crossAxis:o=!1,limiter:i={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=M(e,t),d={x:r,y:a},c=await H(t,l),u=$(O(n)),m=D(u),f=d[m],p=d[u];if(s){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",r=f+c[e],a=f-c[t];f=C(r,E(f,a))}if(o){let e="y"===u?"top":"left",t="y"===u?"bottom":"right",r=p+c[e],a=p-c[t];p=C(r,E(p,a))}let h=i.fn({...t,[m]:f,[u]:p});return{...h,data:{x:h.x-r,y:h.y-a,enabled:{[m]:s,[u]:o}}}}}},eS=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,a,n,s,o;let{placement:i,middlewareData:l,rects:d,initialPlacement:c,platform:u,elements:m}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:y=!0,...v}=M(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let b=O(i),w=$(c),j=O(c)===c,N=await (null==u.isRTL?void 0:u.isRTL(m.floating)),k=h||(j||!y?[z(c)]:function(e){let t=z(e);return[F(e),t,F(t)]}(c)),_="none"!==x;!h&&_&&k.push(...function(e,t,r,a){let n=I(e),s=function(e,t,r){let a=["left","right"],n=["right","left"];switch(e){case"top":case"bottom":if(r)return t?n:a;return t?a:n;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(O(e),"start"===r,a);return n&&(s=s.map(e=>e+"-"+n),t&&(s=s.concat(s.map(F)))),s}(c,y,x,N));let E=[c,...k],C=await H(t,v),S=[],A=(null==(a=l.flip)?void 0:a.overflows)||[];if(f&&S.push(C[b]),p){let e=function(e,t,r){void 0===r&&(r=!1);let a=I(e),n=D($(e)),s=L(n),o="x"===n?a===(r?"end":"start")?"right":"left":"start"===a?"bottom":"top";return t.reference[s]>t.floating[s]&&(o=z(o)),[o,z(o)]}(i,d,N);S.push(C[e[0]],C[e[1]])}if(A=[...A,{placement:i,overflows:S}],!S.every(e=>e<=0)){let e=((null==(n=l.flip)?void 0:n.index)||0)+1,t=E[e];if(t&&("alignment"!==p||w===$(t)||A.every(e=>e.overflows[0]>0&&$(e.placement)===w)))return{data:{index:e,overflows:A},reset:{placement:t}};let r=null==(s=A.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:s.placement;if(!r)switch(g){case"bestFit":{let e=null==(o=A.filter(e=>{if(_){let t=$(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(r=e);break}case"initialPlacement":r=c}if(i!==r)return{reset:{placement:r}}}return{}}}},eA=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:a,placement:n,rects:s,platform:o,elements:i,middlewareData:l}=t,{element:d,padding:c=0}=M(e,t)||{};if(null==d)return{};let u=W(c),m={x:r,y:a},f=D($(n)),p=L(f),h=await o.getDimensions(d),g="y"===f,x=g?"clientHeight":"clientWidth",y=s.reference[p]+s.reference[f]-m[f]-s.floating[p],v=m[f]-s.reference[f],b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(d)),w=b?b[x]:0;w&&await (null==o.isElement?void 0:o.isElement(b))||(w=i.floating[x]||s.floating[p]);let j=w/2-h[p]/2-1,N=E(u[g?"top":"left"],j),k=E(u[g?"bottom":"right"],j),_=w-h[p]-k,S=w/2-h[p]/2+(y/2-v/2),A=C(N,E(S,_)),R=!l.arrow&&null!=I(n)&&S!==A&&s.reference[p]/2-(S<N?N:k)-h[p]/2<0,P=R?S<N?S-N:S-_:0;return{[f]:m[f]+P,data:{[f]:A,centerOffset:S-A-P,...R&&{alignmentOffset:P}},reset:R}}}),eR=(e,t,r)=>{let a=new Map,n={platform:ek,...r},s={...n.platform,_c:a};return q(e,t,{...n,platform:s})};var eP=r(69662);let eT={core:!1,base:!1};function eM({css:e,id:t="react-tooltip-base-styles",type:r="base",ref:a}){var n,s;if(!e||"undefined"==typeof document||eT[r]||"core"===r&&"undefined"!=typeof process&&(null==(n=null==process?void 0:process.env)?void 0:n.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==r&&"undefined"!=typeof process&&(null==(s=null==process?void 0:process.env)?void 0:s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===r&&(t="react-tooltip-core-styles"),a||(a={});let{insertAt:o}=a;if(document.getElementById(t))return;let i=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=t,l.type="text/css","top"===o&&i.firstChild?i.insertBefore(l,i.firstChild):i.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e)),eT[r]=!0}let eO=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:r=null,place:a="top",offset:n=10,strategy:s="absolute",middlewares:o=[eE(Number(n)),eS({fallbackAxisSideDirection:"start"}),eC({padding:5})],border:i,arrowSize:l=8})=>e&&null!==t?r?(o.push(eA({element:r,padding:5})),eR(e,t,{placement:a,strategy:s,middleware:o}).then(({x:e,y:t,placement:r,middlewareData:a})=>{var n,s;let o={left:`${e}px`,top:`${t}px`,border:i},{x:d,y:c}=null!=(n=a.arrow)?n:{x:0,y:0},u=null!=(s=({top:"bottom",right:"left",bottom:"top",left:"right"})[r.split("-")[0]])?s:"bottom",m=0;if(i){let e=`${i}`.match(/(\d+)px/);m=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:o,tooltipArrowStyles:{left:null!=d?`${d}px`:"",top:null!=c?`${c}px`:"",right:"",bottom:"",...i&&{borderBottom:i,borderRight:i},[u]:`-${l/2+m}px`},place:r}})):eR(e,t,{placement:"bottom",strategy:s,middleware:o}).then(({x:e,y:t,placement:r})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:r})):{tooltipStyles:{},tooltipArrowStyles:{},place:a},eI=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),eD=(e,t,r)=>{let a=null,n=function(...n){let s=()=>{a=null,r||e.apply(this,n)};r&&!a&&(e.apply(this,n),a=setTimeout(s,t)),r||(a&&clearTimeout(a),a=setTimeout(s,t))};return n.cancel=()=>{a&&(clearTimeout(a),a=null)},n},eL=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,e$=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>e$(e,t[r]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!eL(e)||!eL(t))return e===t;let r=Object.keys(e),a=Object.keys(t);return r.length===a.length&&r.every(r=>e$(e[r],t[r]))},eF=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let r=t.getPropertyValue(e);return"auto"===r||"scroll"===r})},ez=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(eF(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},eW="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,eK=e=>{e.current&&(clearTimeout(e.current),e.current=null)},eB={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},eq=(0,i.createContext)({getTooltipData:()=>eB});function eH(e="DEFAULT_TOOLTIP_ID"){return(0,i.useContext)(eq).getTooltipData(e)}var eU={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},eV={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let eY=({forwardRef:e,id:t,className:r,classNameArrow:a,variant:n="dark",anchorId:s,anchorSelect:o,place:l="top",offset:d=10,events:c=["hover"],openOnClick:u=!1,positionStrategy:m="absolute",middlewares:f,wrapper:p,delayShow:h=0,delayHide:g=0,float:x=!1,hidden:y=!1,noArrow:v=!1,clickable:b=!1,closeOnEsc:w=!1,closeOnScroll:j=!1,closeOnResize:N=!1,openEvents:k,closeEvents:_,globalCloseEvents:S,imperativeModeOnly:R,style:P,position:T,afterShow:M,afterHide:O,disableTooltip:I,content:D,contentWrapperRef:L,isOpen:$,defaultIsOpen:F=!1,setIsOpen:z,activeAnchor:W,setActiveAnchor:K,border:B,opacity:q,arrowColor:H,arrowSize:U=8,role:V="tooltip"})=>{var Y;let X=(0,i.useRef)(null),J=(0,i.useRef)(null),Z=(0,i.useRef)(null),Q=(0,i.useRef)(null),ee=(0,i.useRef)(null),[et,er]=(0,i.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:l}),[ea,en]=(0,i.useState)(!1),[es,eo]=(0,i.useState)(!1),[ei,el]=(0,i.useState)(null),ec=(0,i.useRef)(!1),eu=(0,i.useRef)(null),{anchorRefs:ef,setActiveAnchor:ep}=eH(t),eh=(0,i.useRef)(!1),[ex,ey]=(0,i.useState)([]),ev=(0,i.useRef)(!1),eb=u||c.includes("click"),ew=eb||(null==k?void 0:k.click)||(null==k?void 0:k.dblclick)||(null==k?void 0:k.mousedown),ej=k?{...k}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!k&&eb&&Object.assign(ej,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eN=_?{..._}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!_&&eb&&Object.assign(eN,{mouseleave:!1,blur:!1,mouseout:!1});let ek=S?{...S}:{escape:w||!1,scroll:j||!1,resize:N||!1,clickOutsideAnchor:ew||!1};R&&(Object.assign(ej,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eN,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(ek,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),eW(()=>(ev.current=!0,()=>{ev.current=!1}),[]);let eE=e=>{ev.current&&(e&&eo(!0),setTimeout(()=>{ev.current&&(null==z||z(e),void 0===$&&en(e))},10))};(0,i.useEffect)(()=>{if(void 0===$)return()=>null;$&&eo(!0);let e=setTimeout(()=>{en($)},10);return()=>{clearTimeout(e)}},[$]),(0,i.useEffect)(()=>{ea!==ec.current&&((eK(ee),ec.current=ea,ea)?null==M||M():ee.current=setTimeout(()=>{eo(!1),el(null),null==O||O()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,r,a]=t;return Number(r)*("ms"===a?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[ea]);let eC=e=>{er(t=>e$(t,e)?t:e)},eS=(e=h)=>{eK(Z),es?eE(!0):Z.current=setTimeout(()=>{eE(!0)},e)},eA=(e=g)=>{eK(Q),Q.current=setTimeout(()=>{eh.current||eE(!1)},e)},eR=e=>{var t;if(!e)return;let r=null!=(t=e.currentTarget)?t:e.target;if(!(null==r?void 0:r.isConnected))return K(null),void ep({current:null});h?eS():eE(!0),K(r),ep({current:r}),eK(Q)},eT=()=>{b?eA(g||100):g?eA():eE(!1),eK(Z)},eM=({x:e,y:t})=>{var r;eO({place:null!=(r=null==ei?void 0:ei.place)?r:l,offset:d,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:X.current,tooltipArrowReference:J.current,strategy:m,middlewares:f,border:B,arrowSize:U}).then(e=>{eC(e)})},eI=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eM(t),eu.current=t},eL=e=>{var t;if(!ea)return;let r=e.target;r.isConnected&&(null==(t=X.current)||!t.contains(r))&&([document.querySelector(`[id='${s}']`),...ex].some(e=>null==e?void 0:e.contains(r))||(eE(!1),eK(Z)))},eF=eD(eR,50,!0),eB=eD(eT,50,!0),eq=e=>{eB.cancel(),eF(e)},eY=()=>{eF.cancel(),eB()},eX=(0,i.useCallback)(()=>{var e,t;let r=null!=(e=null==ei?void 0:ei.position)?e:T;r?eM(r):x?eu.current&&eM(eu.current):(null==W?void 0:W.isConnected)&&eO({place:null!=(t=null==ei?void 0:ei.place)?t:l,offset:d,elementReference:W,tooltipReference:X.current,tooltipArrowReference:J.current,strategy:m,middlewares:f,border:B,arrowSize:U}).then(e=>{ev.current&&eC(e)})},[ea,W,D,P,l,null==ei?void 0:ei.place,d,m,T,null==ei?void 0:ei.position,x,U]);(0,i.useEffect)(()=>{var e,t;let r=new Set(ef);ex.forEach(e=>{(null==I?void 0:I(e))||r.add({current:e})});let a=document.querySelector(`[id='${s}']`);!a||(null==I?void 0:I(a))||r.add({current:a});let n=()=>{eE(!1)},o=ez(W),i=ez(X.current);ek.scroll&&(window.addEventListener("scroll",n),null==o||o.addEventListener("scroll",n),null==i||i.addEventListener("scroll",n));let l=null;ek.resize?window.addEventListener("resize",n):W&&X.current&&(l=function(e,t,r,a){let n;void 0===a&&(a={});let{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:d=!1}=a,c=em(e),u=s||o?[...c?ed(c):[],...ed(t)]:[];u.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),o&&e.addEventListener("resize",r)});let m=c&&l?function(e,t){let r,a=null,n=G(e);function s(){var e;clearTimeout(r),null==(e=a)||e.disconnect(),a=null}return!function o(i,l){void 0===i&&(i=!1),void 0===l&&(l=1),s();let d=e.getBoundingClientRect(),{left:c,top:u,width:m,height:f}=d;if(i||t(),!m||!f)return;let p=A(u),h=A(n.clientWidth-(c+m)),g={rootMargin:-p+"px "+-h+"px "+-A(n.clientHeight-(u+f))+"px "+-A(c)+"px",threshold:C(0,E(1,l))||1},x=!0;function y(t){let a=t[0].intersectionRatio;if(a!==l){if(!x)return o();a?o(!1,a):r=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==a||e_(d,e.getBoundingClientRect())||o(),x=!1}try{a=new IntersectionObserver(y,{...g,root:n.ownerDocument})}catch(e){a=new IntersectionObserver(y,g)}a.observe(e)}(!0),s}(c,r):null,f=-1,p=null;i&&(p=new ResizeObserver(e=>{let[a]=e;a&&a.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),r()}),c&&!d&&p.observe(c),p.observe(t));let h=d?eg(e):null;return d&&function t(){let a=eg(e);h&&!e_(h,a)&&r(),h=a,n=requestAnimationFrame(t)}(),r(),()=>{var e;u.forEach(e=>{s&&e.removeEventListener("scroll",r),o&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=p)||e.disconnect(),p=null,d&&cancelAnimationFrame(n)}}(W,X.current,eX,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let d=e=>{"Escape"===e.key&&eE(!1)};ek.escape&&window.addEventListener("keydown",d),ek.clickOutsideAnchor&&window.addEventListener("click",eL);let c=[],u=e=>!!((null==e?void 0:e.target)&&(null==W?void 0:W.contains(e.target))),m=e=>{ea&&u(e)||eR(e)},f=e=>{ea&&u(e)&&eT()},p=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],h=["click","dblclick","mousedown","mouseup"];Object.entries(ej).forEach(([e,t])=>{t&&(p.includes(e)?c.push({event:e,listener:eq}):h.includes(e)&&c.push({event:e,listener:m}))}),Object.entries(eN).forEach(([e,t])=>{t&&(p.includes(e)?c.push({event:e,listener:eY}):h.includes(e)&&c.push({event:e,listener:f}))}),x&&c.push({event:"pointermove",listener:eI});let g=()=>{eh.current=!0},y=()=>{eh.current=!1,eT()},v=b&&(eN.mouseout||eN.mouseleave);return v&&(null==(e=X.current)||e.addEventListener("mouseover",g),null==(t=X.current)||t.addEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var a;null==(a=r.current)||a.addEventListener(e,t)})}),()=>{var e,t;ek.scroll&&(window.removeEventListener("scroll",n),null==o||o.removeEventListener("scroll",n),null==i||i.removeEventListener("scroll",n)),ek.resize?window.removeEventListener("resize",n):null==l||l(),ek.clickOutsideAnchor&&window.removeEventListener("click",eL),ek.escape&&window.removeEventListener("keydown",d),v&&(null==(e=X.current)||e.removeEventListener("mouseover",g),null==(t=X.current)||t.removeEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var a;null==(a=r.current)||a.removeEventListener(e,t)})})}},[W,eX,es,ef,ex,k,_,S,eb,h,g]),(0,i.useEffect)(()=>{var e,r;let a=null!=(r=null!=(e=null==ei?void 0:ei.anchorSelect)?e:o)?r:"";!a&&t&&(a=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let n=new MutationObserver(e=>{let r=[],n=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?r.push(e.target):e.oldValue===t&&n.push(e.target)),"childList"===e.type){if(W){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(a)try{n.push(...t.filter(e=>e.matches(a))),n.push(...t.flatMap(e=>[...e.querySelectorAll(a)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,W))&&(eo(!1),eE(!1),K(null),eK(Z),eK(Q),!0)})}if(a)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);r.push(...t.filter(e=>e.matches(a))),r.push(...t.flatMap(e=>[...e.querySelectorAll(a)]))}catch(e){}}}),(r.length||n.length)&&ey(e=>[...e.filter(e=>!n.includes(e)),...r])});return n.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{n.disconnect()}},[t,o,null==ei?void 0:ei.anchorSelect,W]),(0,i.useEffect)(()=>{eX()},[eX]),(0,i.useEffect)(()=>{if(!(null==L?void 0:L.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eX())});return e.observe(L.current),()=>{e.disconnect()}},[D,null==L?void 0:L.current]),(0,i.useEffect)(()=>{var e;let t=document.querySelector(`[id='${s}']`),r=[...ex,t];W&&r.includes(W)||K(null!=(e=ex[0])?e:t)},[s,ex,W]),(0,i.useEffect)(()=>(F&&eE(!0),()=>{eK(Z),eK(Q)}),[]),(0,i.useEffect)(()=>{var e;let r=null!=(e=null==ei?void 0:ei.anchorSelect)?e:o;if(!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),r)try{let e=Array.from(document.querySelectorAll(r));ey(e)}catch(e){ey([])}},[t,o,null==ei?void 0:ei.anchorSelect]),(0,i.useEffect)(()=>{Z.current&&(eK(Z),eS(h))},[h]);let eG=null!=(Y=null==ei?void 0:ei.content)?Y:D,eJ=ea&&Object.keys(et.tooltipStyles).length>0;return(0,i.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}el(null!=e?e:null),(null==e?void 0:e.delay)?eS(e.delay):eE(!0)},close:e=>{(null==e?void 0:e.delay)?eA(e.delay):eE(!1)},activeAnchor:W,place:et.place,isOpen:!!(es&&!y&&eG&&eJ)})),es&&!y&&eG?i.createElement(p,{id:t,role:V,className:eP("react-tooltip",eU.tooltip,eV.tooltip,eV[n],r,`react-tooltip__place-${et.place}`,eU[eJ?"show":"closing"],eJ?"react-tooltip__show":"react-tooltip__closing","fixed"===m&&eU.fixed,b&&eU.clickable),onTransitionEnd:e=>{eK(ee),ea||"opacity"!==e.propertyName||(eo(!1),el(null),null==O||O())},style:{...P,...et.tooltipStyles,opacity:void 0!==q&&eJ?q:void 0},ref:X},eG,i.createElement(p,{className:eP("react-tooltip-arrow",eU.arrow,eV.arrow,a,v&&eU.noArrow),style:{...et.tooltipArrowStyles,background:H?`linear-gradient(to right bottom, transparent 50%, ${H} 50%)`:void 0,"--rt-arrow-size":`${U}px`},ref:J})):null},eX=({content:e})=>i.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),eG=i.forwardRef(({id:e,anchorId:t,anchorSelect:r,content:a,html:n,render:s,className:o,classNameArrow:l,variant:d="dark",place:c="top",offset:u=10,wrapper:m="div",children:f=null,events:p=["hover"],openOnClick:h=!1,positionStrategy:g="absolute",middlewares:x,delayShow:y=0,delayHide:v=0,float:b=!1,hidden:w=!1,noArrow:j=!1,clickable:N=!1,closeOnEsc:k=!1,closeOnScroll:_=!1,closeOnResize:E=!1,openEvents:C,closeEvents:S,globalCloseEvents:A,imperativeModeOnly:R=!1,style:P,position:T,isOpen:M,defaultIsOpen:O=!1,disableStyleInjection:I=!1,border:D,opacity:L,arrowColor:$,arrowSize:F,setIsOpen:z,afterShow:W,afterHide:K,disableTooltip:B,role:q="tooltip"},H)=>{let[U,V]=(0,i.useState)(a),[Y,X]=(0,i.useState)(n),[G,J]=(0,i.useState)(c),[Z,Q]=(0,i.useState)(d),[ee,et]=(0,i.useState)(u),[er,ea]=(0,i.useState)(y),[en,es]=(0,i.useState)(v),[eo,ei]=(0,i.useState)(b),[el,ed]=(0,i.useState)(w),[ec,eu]=(0,i.useState)(m),[em,ef]=(0,i.useState)(p),[ep,eh]=(0,i.useState)(g),[eg,ex]=(0,i.useState)(null),[ey,ev]=(0,i.useState)(null),eb=(0,i.useRef)(I),{anchorRefs:ew,activeAnchor:ej}=eH(e),eN=e=>null==e?void 0:e.getAttributeNames().reduce((t,r)=>{var a;return r.startsWith("data-tooltip-")&&(t[r.replace(/^data-tooltip-/,"")]=null!=(a=null==e?void 0:e.getAttribute(r))?a:null),t},{}),ek=e=>{let t={place:e=>{J(null!=e?e:c)},content:e=>{V(null!=e?e:a)},html:e=>{X(null!=e?e:n)},variant:e=>{Q(null!=e?e:d)},offset:e=>{et(null===e?u:Number(e))},wrapper:e=>{eu(null!=e?e:m)},events:e=>{let t=null==e?void 0:e.split(" ");ef(null!=t?t:p)},"position-strategy":e=>{eh(null!=e?e:g)},"delay-show":e=>{ea(null===e?y:Number(e))},"delay-hide":e=>{es(null===e?v:Number(e))},float:e=>{ei(null===e?b:"true"===e)},hidden:e=>{ed(null===e?w:"true"===e)},"class-name":e=>{ex(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,r])=>{var a;null==(a=t[e])||a.call(t,r)})};(0,i.useEffect)(()=>{V(a)},[a]),(0,i.useEffect)(()=>{X(n)},[n]),(0,i.useEffect)(()=>{J(c)},[c]),(0,i.useEffect)(()=>{Q(d)},[d]),(0,i.useEffect)(()=>{et(u)},[u]),(0,i.useEffect)(()=>{ea(y)},[y]),(0,i.useEffect)(()=>{es(v)},[v]),(0,i.useEffect)(()=>{ei(b)},[b]),(0,i.useEffect)(()=>{ed(w)},[w]),(0,i.useEffect)(()=>{eh(g)},[g]),(0,i.useEffect)(()=>{eb.current!==I&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[I]),(0,i.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===I,disableBase:I}}))},[]),(0,i.useEffect)(()=>{var a;let n=new Set(ew),s=r;if(!s&&e&&(s=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),s)try{document.querySelectorAll(s).forEach(e=>{n.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${s}" is not a valid CSS selector`)}let o=document.querySelector(`[id='${t}']`);if(o&&n.add({current:o}),!n.size)return()=>null;let i=null!=(a=null!=ey?ey:o)?a:ej.current,l=new MutationObserver(e=>{e.forEach(e=>{var t;i&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&ek(eN(i))})});return i&&(ek(eN(i)),l.observe(i,{attributes:!0,childList:!1,subtree:!1})),()=>{l.disconnect()}},[ew,ej,ey,t,r]),(0,i.useEffect)(()=>{(null==P?void 0:P.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),D&&!eI("border",`${D}`)&&console.warn(`[react-tooltip] "${D}" is not a valid \`border\`.`),(null==P?void 0:P.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),L&&!eI("opacity",`${L}`)&&console.warn(`[react-tooltip] "${L}" is not a valid \`opacity\`.`)},[]);let e_=f,eE=(0,i.useRef)(null);if(s){let e=s({content:(null==ey?void 0:ey.getAttribute("data-tooltip-content"))||U||null,activeAnchor:ey});e_=e?i.createElement("div",{ref:eE,className:"react-tooltip-content-wrapper"},e):null}else U&&(e_=U);Y&&(e_=i.createElement(eX,{content:Y}));let eC={forwardRef:H,id:e,anchorId:t,anchorSelect:r,className:eP(o,eg),classNameArrow:l,content:e_,contentWrapperRef:eE,place:G,variant:Z,offset:ee,wrapper:ec,events:em,openOnClick:h,positionStrategy:ep,middlewares:x,delayShow:er,delayHide:en,float:eo,hidden:el,noArrow:j,clickable:N,closeOnEsc:k,closeOnScroll:_,closeOnResize:E,openEvents:C,closeEvents:S,globalCloseEvents:A,imperativeModeOnly:R,style:P,position:T,isOpen:M,defaultIsOpen:O,border:D,opacity:L,arrowColor:$,arrowSize:F,setIsOpen:z,afterShow:W,afterHide:K,disableTooltip:B,activeAnchor:ey,setActiveAnchor:e=>ev(e),role:q};return i.createElement(eY,{...eC})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||eM({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||eM({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});var eJ=r(50181),eZ=r(20404),eQ=r(5097);function e0(){return(0,o.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-64 animate-pulse"})]})]})}),(0,o.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,o.jsxs)("div",{className:"md:col-span-2",children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]})]}),(0,o.jsx)("div",{className:"mt-6",children:(0,o.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 animate-pulse"}),(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,o.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,o.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-lg animate-pulse"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"}),(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]})]}),(0,o.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 animate-pulse"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-18 animate-pulse"})]})]}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-28 mb-2 animate-pulse"}),(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded w-36 animate-pulse"}),(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-28 animate-pulse"})]}),(0,o.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,o.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-48 animate-pulse"})]}),(0,o.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16 animate-pulse"})]},e))})]})]})}function e1(){return(0,o.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-56 animate-pulse"})]})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,o.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,o.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]})},e))})]})}var e2=r(60925),e5=r(36721),e4=r(9776);let e3=(0,i.forwardRef)(({className:e="",variant:t="default",size:r="default",loading:a=!1,icon:n,iconPosition:s="left",children:i,disabled:l,...d},c)=>{let u={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},m=l||a;return(0,o.jsxs)("button",{ref:c,className:`inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed ${{default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[t]} ${{default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[r]} ${e}`,disabled:m,...d,children:[a&&(0,o.jsx)(e4.Ay,{size:"lg"===r?"md":"sm",className:"mr-2"}),!a&&n&&"left"===s&&(0,o.jsx)("span",{className:`${u[r]} mr-2`,children:n}),i,!a&&n&&"right"===s&&(0,o.jsx)("span",{className:`${u[r]} ml-2`,children:n})]})});e3.displayName="Button";let e6=(0,i.forwardRef)(({className:e="",variant:t="default",hover:r=!1,padding:a="md",children:n,...s},i)=>(0,o.jsx)("div",{ref:i,className:`rounded-xl transition-all duration-200 ${{default:"card",glass:"glass",gradient:"gradient-surface border border-white/10"}[t]} ${{sm:"p-4",md:"p-6",lg:"p-8"}[a]} ${r?"hover:shadow-md hover:-translate-y-1 cursor-pointer":""} ${e}`,...s,children:n}));e6.displayName="Card";let e9=({className:e="",title:t,subtitle:r,action:a,children:n,...s})=>(0,o.jsxs)("div",{className:`flex items-center justify-between mb-6 ${e}`,...s,children:[(0,o.jsxs)("div",{children:[t&&(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:t}),r&&(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:r}),n]}),a&&(0,o.jsx)("div",{children:a})]}),e8=({className:e="",children:t,...r})=>(0,o.jsx)("h3",{className:`text-xl font-semibold text-gray-900 dark:text-white ${e}`,...r,children:t}),e7=({className:e="",children:t,...r})=>(0,o.jsx)("div",{className:`${e}`,...r,children:t}),te=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,tt=function(){for(var e,t,r=0,a="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(a=e(t[r]))&&(n&&(n+=" "),n+=a)}else for(a in t)t[a]&&(n&&(n+=" "),n+=a);return n}(e))&&(a&&(a+=" "),a+=t);return a},tr=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return tt(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:s}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],a=null==s?void 0:s[e];if(null===t)return null;let o=te(t)||te(a);return n[e][o]}),i=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return tt(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...i}[t]):({...s,...i})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)},ta=tr("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function tn({className:e,variant:t,...r}){return(0,o.jsx)("div",{className:`${ta({variant:t})} ${e||""}`,...r})}var ts=r(62688);let to=(0,ts.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),ti=(0,ts.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),tl=(0,ts.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var td=r(93613),tc=r(51215);Array(12).fill(0);let tu=1;class tm{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...a}=e,n="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:tu++,s=this.toasts.find(e=>e.id===n),o=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(n)&&this.dismissedToasts.delete(n),s?this.toasts=this.toasts.map(t=>t.id===n?(this.publish({...t,...e,id:n,title:r}),{...t,...e,id:n,dismissible:o,title:r}):t):this.addToast({title:r,...a,dismissible:o,id:n}),n},this.dismiss=e=>(e?(this.dismissedToasts.add(e),requestAnimationFrame(()=>this.subscribers.forEach(t=>t({id:e,dismiss:!0})))):this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r,a;if(!t)return;void 0!==t.loading&&(a=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let n=Promise.resolve(e instanceof Function?e():e),s=void 0!==a,o=n.then(async e=>{if(r=["resolve",e],i.isValidElement(e))s=!1,this.create({id:a,type:"default",message:e});else if(tp(e)&&!e.ok){s=!1;let r="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,n="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description,o="object"!=typeof r||i.isValidElement(r)?{message:r}:r;this.create({id:a,type:"error",description:n,...o})}else if(e instanceof Error){s=!1;let r="function"==typeof t.error?await t.error(e):t.error,n="function"==typeof t.description?await t.description(e):t.description,o="object"!=typeof r||i.isValidElement(r)?{message:r}:r;this.create({id:a,type:"error",description:n,...o})}else if(void 0!==t.success){s=!1;let r="function"==typeof t.success?await t.success(e):t.success,n="function"==typeof t.description?await t.description(e):t.description,o="object"!=typeof r||i.isValidElement(r)?{message:r}:r;this.create({id:a,type:"success",description:n,...o})}}).catch(async e=>{if(r=["reject",e],void 0!==t.error){s=!1;let r="function"==typeof t.error?await t.error(e):t.error,n="function"==typeof t.description?await t.description(e):t.description,o="object"!=typeof r||i.isValidElement(r)?{message:r}:r;this.create({id:a,type:"error",description:n,...o})}}).finally(()=>{s&&(this.dismiss(a),a=void 0),null==t.finally||t.finally.call(t)}),l=()=>new Promise((e,t)=>o.then(()=>"reject"===r[0]?t(r[1]):e(r[1])).catch(t));return"string"!=typeof a&&"number"!=typeof a?{unwrap:l}:Object.assign(a,{unwrap:l})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||tu++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let tf=new tm,tp=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,th=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||tu++;return tf.addToast({title:e,...t,id:r}),r},{success:tf.success,info:tf.info,warning:tf.warning,error:tf.error,custom:tf.custom,message:tf.message,promise:tf.promise,dismiss:tf.dismiss,loading:tf.loading},{getHistory:()=>tf.toasts,getToasts:()=>tf.getActiveToasts()});!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let tg=(0,ts.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),tx=(0,ts.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),ty=(0,ts.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),tv=(0,ts.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),tb=(0,ts.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),tw=(0,ts.A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),tj=(0,ts.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),tN=Symbol.for("constructDateFrom");function tk(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&tN in e?e[tN](t):e instanceof Date?new e.constructor(t):new Date(t)}let t_={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function tE(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let tC={date:tE({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:tE({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:tE({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},tS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function tA(e){return(t,r)=>{let a;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,n=r?.width?String(r.width):t;a=e.formattingValues[n]||e.formattingValues[t]}else{let t=e.defaultWidth,n=r?.width?String(r.width):e.defaultWidth;a=e.values[n]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function tR(e){return(t,r={})=>{let a,n=r.width,s=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],o=t.match(s);if(!o)return null;let i=o[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(i)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(i));return a=e.valueCallback?e.valueCallback(d):d,{value:a=r.valueCallback?r.valueCallback(a):a,rest:t.slice(i.length)}}}let tP={code:"en-US",formatDistance:(e,t,r)=>{let a,n=t_[e];if(a="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",t.toString()),r?.addSuffix)if(r.comparison&&r.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:tC,formatRelative:(e,t,r,a)=>tS[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),a=r%100;if(a>20||a<10)switch(a%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:tA({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:tA({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:tA({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:tA({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:tA({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let a=t.match(e.matchPattern);if(!a)return null;let n=a[0],s=t.match(e.parsePattern);if(!s)return null;let o=e.valueCallback?e.valueCallback(s[0]):s[0];return{value:o=r.valueCallback?r.valueCallback(o):o,rest:t.slice(n.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:tR({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:tR({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:tR({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:tR({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:tR({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},tT={};function tM(e,t){return tk(t||e,e)}function tO(e){let t=tM(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function tI(e,...t){let r=tk.bind(null,e||t.find(e=>"object"==typeof e));return t.map(r)}function tD(e,t){let r=tM(e)-tM(t);return r<0?-1:r>0?1:r}function tL(e,t){return function(e,t,r){let a,n=r?.locale??tT.locale??tP,s=tD(e,t);if(isNaN(s))throw RangeError("Invalid time value");let o=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:s}),[i,l]=tI(r?.in,...s>0?[t,e]:[e,t]),d=function(e,t,r){var a;return(a=void 0,e=>{let t=(a?Math[a]:Math.trunc)(e);return 0===t?0:t})((tM(e)-tM(t))/1e3)}(l,i),c=Math.round((d-(tO(l)-tO(i))/1e3)/60);if(c<2)if(r?.includeSeconds)if(d<5)return n.formatDistance("lessThanXSeconds",5,o);else if(d<10)return n.formatDistance("lessThanXSeconds",10,o);else if(d<20)return n.formatDistance("lessThanXSeconds",20,o);else if(d<40)return n.formatDistance("halfAMinute",0,o);else if(d<60)return n.formatDistance("lessThanXMinutes",1,o);else return n.formatDistance("xMinutes",1,o);else if(0===c)return n.formatDistance("lessThanXMinutes",1,o);else return n.formatDistance("xMinutes",c,o);if(c<45)return n.formatDistance("xMinutes",c,o);if(c<90)return n.formatDistance("aboutXHours",1,o);if(c<1440){let e=Math.round(c/60);return n.formatDistance("aboutXHours",e,o)}if(c<2520)return n.formatDistance("xDays",1,o);else if(c<43200){let e=Math.round(c/1440);return n.formatDistance("xDays",e,o)}else if(c<86400)return a=Math.round(c/43200),n.formatDistance("aboutXMonths",a,o);if((a=function(e,t,r){let[a,n,s]=tI(void 0,e,e,t),o=tD(n,s),i=Math.abs(function(e,t,r){let[a,n]=tI(void 0,e,t);return 12*(a.getFullYear()-n.getFullYear())+(a.getMonth()-n.getMonth())}(n,s));if(i<1)return 0;1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-o*i);let l=tD(n,s)===-o;(function(e,t){let r=tM(e,void 0);return+function(e,t){let r=tM(e,t?.in);return r.setHours(23,59,59,999),r}(r,void 0)==+function(e,t){let r=tM(e,t?.in),a=r.getMonth();return r.setFullYear(r.getFullYear(),a+1,0),r.setHours(23,59,59,999),r}(r,t)})(a)&&1===i&&1===tD(a,s)&&(l=!1);let d=o*(i-l);return 0===d?0:d}(l,i))<12){let e=Math.round(c/43200);return n.formatDistance("xMonths",e,o)}{let e=a%12,t=Math.trunc(a/12);return e<3?n.formatDistance("aboutXYears",t,o):e<9?n.formatDistance("overXYears",t,o):n.formatDistance("almostXYears",t+1,o)}}(e,tk(e,Date.now()),t)}function t$({apiKey:e,onEdit:t,onRevoke:r,onViewUsage:a}){let n=async e=>{try{await navigator.clipboard.writeText(e),th.success("API key copied to clipboard")}catch(e){th.error("Failed to copy API key")}},s=e.expires_at&&new Date(e.expires_at)<new Date,i="active"===e.status&&!s;return(0,o.jsxs)(e6,{className:`transition-all duration-200 hover:shadow-md ${!i?"opacity-75":""}`,children:[(0,o.jsx)(e9,{className:"pb-3",children:(0,o.jsxs)("div",{className:"flex items-start justify-between",children:[(0,o.jsxs)("div",{className:"space-y-1",children:[(0,o.jsx)(e8,{className:"text-lg font-semibold",children:e.key_name}),(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:["Configuration: ",e.custom_api_configs.name]})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(tn,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800 border-green-200";case"inactive":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"revoked":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(e.status),children:e.status}),s&&(0,o.jsx)(tn,{className:"bg-red-100 text-red-800 border-red-200",children:"Expired"})]})]})}),(0,o.jsxs)(e7,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"API Key (Masked)"}),(0,o.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border",children:[(0,o.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-600",children:[e.key_prefix,"_","*".repeat(28),e.masked_key?.slice(-4)||"xxxx"]}),(0,o.jsx)(e3,{variant:"ghost",size:"sm",onClick:()=>n(`${e.key_prefix}_${"*".repeat(28)}${e.masked_key?.slice(-4)||"xxxx"}`),className:"h-8 w-8 p-0",title:"Copy masked key (for reference only)",children:(0,o.jsx)(tg,{className:"h-4 w-4"})})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-600 bg-amber-50 p-2 rounded",children:[(0,o.jsx)("span",{children:"⚠️"}),(0,o.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Permissions"}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.permissions.chat&&(0,o.jsx)(tn,{variant:"secondary",className:"text-xs",children:"Chat Completions"}),e.permissions.streaming&&(0,o.jsx)(tn,{variant:"secondary",className:"text-xs",children:"Streaming"}),e.permissions.all_models&&(0,o.jsx)(tn,{variant:"secondary",className:"text-xs",children:"All Models"})]})]}),(e.allowed_ips.length>0||e.allowed_domains.length>0)&&(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,o.jsx)(tx,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,o.jsxs)("div",{className:"space-y-1 text-xs",children:[e.allowed_ips.length>0&&(0,o.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,o.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,o.jsx)("span",{children:e.allowed_ips.join(", ")})]}),e.allowed_domains.length>0&&(0,o.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,o.jsx)(ty,{className:"h-3 w-3"}),(0,o.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,o.jsx)("span",{children:e.allowed_domains.join(", ")})]})]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,o.jsx)(tv,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Total Requests:"}),(0,o.jsx)("span",{className:"ml-2 font-semibold",children:e.total_requests.toLocaleString()})]}),e.last_used_at&&(0,o.jsxs)("div",{children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Last Used:"}),(0,o.jsx)("span",{className:"ml-2 font-semibold",children:tL(new Date(e.last_used_at),{addSuffix:!0})})]})]})]}),e.expires_at&&(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,o.jsx)(tb,{className:"h-4 w-4"}),"Expiration"]}),(0,o.jsxs)("div",{className:"text-sm",children:[(0,o.jsxs)("span",{className:`font-semibold ${s?"text-red-600":"text-gray-900"}`,children:[new Date(e.expires_at).toLocaleDateString()," at"," ",new Date(e.expires_at).toLocaleTimeString()]}),!s&&(0,o.jsxs)("span",{className:"ml-2 text-gray-600",children:["(",tL(new Date(e.expires_at),{addSuffix:!0}),")"]})]})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsxs)(e3,{variant:"outline",size:"sm",onClick:()=>a(e.id),className:"text-xs",children:[(0,o.jsx)(tv,{className:"h-3 w-3 mr-1"}),"View Usage"]}),i&&(0,o.jsxs)(e3,{variant:"outline",size:"sm",onClick:()=>t(e),className:"text-xs",children:[(0,o.jsx)(tw,{className:"h-3 w-3 mr-1"}),"Edit"]})]}),"revoked"!==e.status&&(0,o.jsxs)(e3,{variant:"destructive",size:"sm",onClick:()=>r(e.id),className:"text-xs",children:[(0,o.jsx)(tj,{className:"h-3 w-3 mr-1"}),"Revoke"]})]})]})]})}function tF(e,t,{checkForDefaultPrevented:r=!0}={}){return function(a){if(e?.(a),!1===r||!a.defaultPrevented)return t?.(a)}}function tz(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function tW(...e){return t=>{let r=!1,a=e.map(e=>{let a=tz(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():tz(e[t],null)}}}}function tK(...e){return i.useCallback(tW(...e),e)}function tB(e,t=[]){let r=[],a=()=>{let t=r.map(e=>i.createContext(e));return function(r){let a=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return a.scopeName=e,[function(t,a){let n=i.createContext(a),s=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[s]||n,c=i.useMemo(()=>l,Object.values(l));return(0,o.jsx)(d.Provider,{value:c,children:a})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[s]||n,d=i.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:a})=>{let n=r(e)[`__scope${a}`];return{...t,...n}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(a,...t)]}var tq=globalThis?.document?i.useLayoutEffect:()=>{},tH=l[" useId ".trim().toString()]||(()=>void 0),tU=0;function tV(e){let[t,r]=i.useState(tH());return tq(()=>{e||r(e=>e??String(tU++))},[e]),e||(t?`radix-${t}`:"")}var tY=l[" useInsertionEffect ".trim().toString()]||tq;function tX({prop:e,defaultProp:t,onChange:r=()=>{},caller:a}){let[n,s,o]=function({defaultProp:e,onChange:t}){let[r,a]=i.useState(e),n=i.useRef(r),s=i.useRef(t);return tY(()=>{s.current=t},[t]),i.useEffect(()=>{n.current!==r&&(s.current?.(r),n.current=r)},[r,n]),[r,a,s]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:n;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${a} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,a])}return[d,i.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else s(t)},[l,e,s,o])]}function tG(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...a}=e;if(i.isValidElement(r)){var n;let e,s,o=(n=r,(s=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(s=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),l=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==i.Fragment&&(l.ref=t?tW(t,o):o),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:a,...n}=e,s=i.Children.toArray(a),l=s.find(tZ);if(l){let e=l.props.children,a=s.map(t=>t!==l?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...n,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,a):null})}return(0,o.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}Symbol("RADIX:SYNC_STATE");var tJ=Symbol("radix.slottable");function tZ(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===tJ}var tQ=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=tG(`Primitive.${t}`),a=i.forwardRef((e,a)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?r:t,{...s,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function t0(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var t1="dismissableLayer.update",t2=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),t5=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:l,onInteractOutside:d,onDismiss:c,...u}=e,m=i.useContext(t2),[f,p]=i.useState(null),h=f?.ownerDocument??globalThis?.document,[,g]=i.useState({}),x=tK(t,e=>p(e)),y=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),b=y.indexOf(v),w=f?y.indexOf(f):-1,j=m.layersWithOutsidePointerEventsDisabled.size>0,N=w>=b,k=function(e,t=globalThis?.document){let r=t0(e),a=i.useRef(!1),n=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let a=function(){t3("dismissableLayer.pointerDownOutside",r,s,{discrete:!0})},s={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",n.current),n.current=a,t.addEventListener("click",n.current,{once:!0})):a()}else t.removeEventListener("click",n.current);a.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",e),t.removeEventListener("click",n.current)}},[t,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));N&&!r&&(s?.(e),d?.(e),e.defaultPrevented||c?.())},h),_=function(e,t=globalThis?.document){let r=t0(e),a=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!a.current&&t3("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...m.branches].some(e=>e.contains(t))&&(l?.(e),d?.(e),e.defaultPrevented||c?.())},h);return!function(e,t=globalThis?.document){let r=t0(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{w===m.layers.size-1&&(a?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},h),i.useEffect(()=>{if(f)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(f)),m.layers.add(f),t4(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=n)}},[f,h,r,m]),i.useEffect(()=>()=>{f&&(m.layers.delete(f),m.layersWithOutsidePointerEventsDisabled.delete(f),t4())},[f,m]),i.useEffect(()=>{let e=()=>g({});return document.addEventListener(t1,e),()=>document.removeEventListener(t1,e)},[]),(0,o.jsx)(tQ.div,{...u,ref:x,style:{pointerEvents:j?N?"auto":"none":void 0,...e.style},onFocusCapture:tF(e.onFocusCapture,_.onFocusCapture),onBlurCapture:tF(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:tF(e.onPointerDownCapture,k.onPointerDownCapture)})});function t4(){let e=new CustomEvent(t1);document.dispatchEvent(e)}function t3(e,t,r,{discrete:a}){let n=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&n.addEventListener(e,t,{once:!0}),a)n&&tc.flushSync(()=>n.dispatchEvent(s));else n.dispatchEvent(s)}t5.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(t2),a=i.useRef(null),n=tK(t,a);return i.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,o.jsx)(tQ.div,{...e,ref:n})}).displayName="DismissableLayerBranch";var t6="focusScope.autoFocusOnMount",t9="focusScope.autoFocusOnUnmount",t8={bubbles:!1,cancelable:!0},t7=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:a=!1,onMountAutoFocus:n,onUnmountAutoFocus:s,...l}=e,[d,c]=i.useState(null),u=t0(n),m=t0(s),f=i.useRef(null),p=tK(t,e=>c(e)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(a){let e=function(e){if(h.paused||!d)return;let t=e.target;d.contains(t)?f.current=t:rr(f.current,{select:!0})},t=function(e){if(h.paused||!d)return;let t=e.relatedTarget;null!==t&&(d.contains(t)||rr(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&rr(d)});return d&&r.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[a,d,h.paused]),i.useEffect(()=>{if(d){ra.add(h);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(t6,t8);d.addEventListener(t6,u),d.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let a of e)if(rr(a,{select:t}),document.activeElement!==r)return}(re(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&rr(d))}return()=>{d.removeEventListener(t6,u),setTimeout(()=>{let t=new CustomEvent(t9,t8);d.addEventListener(t9,m),d.dispatchEvent(t),t.defaultPrevented||rr(e??document.body,{select:!0}),d.removeEventListener(t9,m),ra.remove(h)},0)}}},[d,u,m,h]);let g=i.useCallback(e=>{if(!r&&!a||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[a,s]=function(e){let t=re(e);return[rt(t,e),rt(t.reverse(),e)]}(t);a&&s?e.shiftKey||n!==s?e.shiftKey&&n===a&&(e.preventDefault(),r&&rr(s,{select:!0})):(e.preventDefault(),r&&rr(a,{select:!0})):n===t&&e.preventDefault()}},[r,a,h.paused]);return(0,o.jsx)(tQ.div,{tabIndex:-1,...l,ref:p,onKeyDown:g})});function re(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function rt(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function rr(e,{select:t=!1}={}){if(e&&e.focus){var r;let a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}t7.displayName="FocusScope";var ra=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=rn(e,t)).unshift(t)},remove(t){e=rn(e,t),e[0]?.resume()}}}();function rn(e,t){let r=[...e],a=r.indexOf(t);return -1!==a&&r.splice(a,1),r}var rs=i.forwardRef((e,t)=>{let{container:r,...a}=e,[n,s]=i.useState(!1);tq(()=>s(!0),[]);let l=r||n&&globalThis?.document?.body;return l?tc.createPortal((0,o.jsx)(tQ.div,{...a,ref:t}),l):null});rs.displayName="Portal";var ro=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[a,n]=i.useState(),s=i.useRef(null),o=i.useRef(e),l=i.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>r[e][t]??e,t));return i.useEffect(()=>{let e=ri(s.current);l.current="mounted"===d?e:"none"},[d]),tq(()=>{let t=s.current,r=o.current;if(r!==e){let a=l.current,n=ri(t);e?c("MOUNT"):"none"===n||t?.display==="none"?c("UNMOUNT"):r&&a!==n?c("ANIMATION_OUT"):c("UNMOUNT"),o.current=e}},[e,c]),tq(()=>{if(a){let e,t=a.ownerDocument.defaultView??window,r=r=>{let n=ri(s.current).includes(r.animationName);if(r.target===a&&n&&(c("ANIMATION_END"),!o.current)){let r=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=r)})}},n=e=>{e.target===a&&(l.current=ri(s.current))};return a.addEventListener("animationstart",n),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",n),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}c("ANIMATION_END")},[a,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:i.useCallback(e=>{s.current=e?getComputedStyle(e):null,n(e)},[])}}(t),n="function"==typeof r?r({present:a.isPresent}):i.Children.only(r),s=tK(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof r||a.isPresent?i.cloneElement(n,{ref:s}):null};function ri(e){return e?.animationName||"none"}ro.displayName="Presence";var rl=0;function rd(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var rc=function(){return(rc=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function ru(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r}Object.create;Object.create;var rm=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),rf="width-before-scroll-bar";function rp(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var rh="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,rg=new WeakMap;function rx(e){return e}var ry=function(e){void 0===e&&(e={});var t,r,a,n,s=(t=null,void 0===r&&(r=rx),a=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=r(e,n);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(n=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){n=!0;var t=[];if(a.length){var r=a;a=[],r.forEach(e),t=a}var s=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(s)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}});return s.options=rc({async:!0,ssr:!1},e),s}(),rv=function(){},rb=i.forwardRef(function(e,t){var r,a,n,s,o=i.useRef(null),l=i.useState({onScrollCapture:rv,onWheelCapture:rv,onTouchMoveCapture:rv}),d=l[0],c=l[1],u=e.forwardProps,m=e.children,f=e.className,p=e.removeScrollBar,h=e.enabled,g=e.shards,x=e.sideCar,y=e.noRelative,v=e.noIsolation,b=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,k=ru(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=(r=[o,t],a=function(e){return r.forEach(function(t){return rp(t,e)})},(n=(0,i.useState)(function(){return{value:null,callback:a,facade:{get current(){return n.value},set current(value){var e=n.value;e!==value&&(n.value=value,n.callback(value,e))}}}})[0]).callback=a,s=n.facade,rh(function(){var e=rg.get(s);if(e){var t=new Set(e),a=new Set(r),n=s.current;t.forEach(function(e){a.has(e)||rp(e,null)}),a.forEach(function(e){t.has(e)||rp(e,n)})}rg.set(s,r)},[r]),s),E=rc(rc({},k),d);return i.createElement(i.Fragment,null,h&&i.createElement(x,{sideCar:ry,removeScrollBar:p,shards:g,noRelative:y,noIsolation:v,inert:b,setCallbacks:c,allowPinchZoom:!!w,lockRef:o,gapMode:N}),u?i.cloneElement(i.Children.only(m),rc(rc({},E),{ref:_})):i.createElement(void 0===j?"div":j,rc({},E,{className:f,ref:_}),m))});rb.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},rb.classNames={fullWidth:rf,zeroRight:rm};var rw=function(e){var t=e.sideCar,r=ru(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return i.createElement(a,rc({},r))};rw.isSideCarExport=!0;var rj=function(){var e=0,t=null;return{add:function(a){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||r.nc;return t&&e.setAttribute("nonce",t),e}())){var n,o;(n=t).styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rN=function(){var e=rj();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},rk=function(){var e=rN();return function(t){return e(t.styles,t.dynamic),null}},r_={left:0,top:0,right:0,gap:0},rE=function(e){return parseInt(e||"",10)||0},rC=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],a=t["padding"===e?"paddingTop":"marginTop"],n=t["padding"===e?"paddingRight":"marginRight"];return[rE(r),rE(a),rE(n)]},rS=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return r_;var t=rC(e),r=document.documentElement.clientWidth,a=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,a-r+t[2]-t[0])}},rA=rk(),rR="data-scroll-locked",rP=function(e,t,r,a){var n=e.left,s=e.top,o=e.right,i=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(a,";\n   padding-right: ").concat(i,"px ").concat(a,";\n  }\n  body[").concat(rR,"] {\n    overflow: hidden ").concat(a,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(a,";"),"margin"===r&&"\n    padding-left: ".concat(n,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(a,";\n    "),"padding"===r&&"padding-right: ".concat(i,"px ").concat(a,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(rm," {\n    right: ").concat(i,"px ").concat(a,";\n  }\n  \n  .").concat(rf," {\n    margin-right: ").concat(i,"px ").concat(a,";\n  }\n  \n  .").concat(rm," .").concat(rm," {\n    right: 0 ").concat(a,";\n  }\n  \n  .").concat(rf," .").concat(rf," {\n    margin-right: 0 ").concat(a,";\n  }\n  \n  body[").concat(rR,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},rT=function(){var e=parseInt(document.body.getAttribute(rR)||"0",10);return isFinite(e)?e:0},rM=function(){i.useEffect(function(){return document.body.setAttribute(rR,(rT()+1).toString()),function(){var e=rT()-1;e<=0?document.body.removeAttribute(rR):document.body.setAttribute(rR,e.toString())}},[])},rO=function(e){var t=e.noRelative,r=e.noImportant,a=e.gapMode,n=void 0===a?"margin":a;rM();var s=i.useMemo(function(){return rS(n)},[n]);return i.createElement(rA,{styles:rP(s,!t,n,r?"":"!important")})},rI=!1;if("undefined"!=typeof window)try{var rD=Object.defineProperty({},"passive",{get:function(){return rI=!0,!0}});window.addEventListener("test",rD,rD),window.removeEventListener("test",rD,rD)}catch(e){rI=!1}var rL=!!rI&&{passive:!1},r$=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},rF=function(e,t){var r=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),rz(e,a)){var n=rW(e,a);if(n[1]>n[2])return!0}a=a.parentNode}while(a&&a!==r.body);return!1},rz=function(e,t){return"v"===e?r$(t,"overflowY"):r$(t,"overflowX")},rW=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rK=function(e,t,r,a,n){var s,o=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),i=o*a,l=r.target,d=t.contains(l),c=!1,u=i>0,m=0,f=0;do{if(!l)break;var p=rW(e,l),h=p[0],g=p[1]-p[2]-o*h;(h||g)&&rz(e,l)&&(m+=g,f+=h);var x=l.parentNode;l=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return u&&(n&&1>Math.abs(m)||!n&&i>m)?c=!0:!u&&(n&&1>Math.abs(f)||!n&&-i>f)&&(c=!0),c},rB=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rq=function(e){return[e.deltaX,e.deltaY]},rH=function(e){return e&&"current"in e?e.current:e},rU=0,rV=[];let rY=(a=function(e){var t=i.useRef([]),r=i.useRef([0,0]),a=i.useRef(),n=i.useState(rU++)[0],s=i.useState(rk)[0],o=i.useRef(e);i.useEffect(function(){o.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(n));var t=(function(e,t,r){if(r||2==arguments.length)for(var a,n=0,s=t.length;n<s;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(rH),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(n))}),function(){document.body.classList.remove("block-interactivity-".concat(n)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(n))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!o.current.allowPinchZoom;var n,s=rB(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-s[0],d="deltaY"in e?e.deltaY:i[1]-s[1],c=e.target,u=Math.abs(l)>Math.abs(d)?"h":"v";if("touches"in e&&"h"===u&&"range"===c.type)return!1;var m=rF(u,c);if(!m)return!0;if(m?n=u:(n="v"===u?"h":"v",m=rF(u,c)),!m)return!1;if(!a.current&&"changedTouches"in e&&(l||d)&&(a.current=n),!n)return!0;var f=a.current||n;return rK(f,t,e,"h"===f?l:d,!0)},[]),d=i.useCallback(function(e){if(rV.length&&rV[rV.length-1]===s){var r="deltaY"in e?rq(e):rB(e),a=t.current.filter(function(t){var a;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(a=t.delta,a[0]===r[0]&&a[1]===r[1])})[0];if(a&&a.should){e.cancelable&&e.preventDefault();return}if(!a){var n=(o.current.shards||[]).map(rH).filter(Boolean).filter(function(t){return t.contains(e.target)});(n.length>0?l(e,n[0]):!o.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,a,n){var s={name:e,delta:r,target:a,should:n,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(a)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),u=i.useCallback(function(e){r.current=rB(e),a.current=void 0},[]),m=i.useCallback(function(t){c(t.type,rq(t),t.target,l(t,e.lockRef.current))},[]),f=i.useCallback(function(t){c(t.type,rB(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return rV.push(s),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:f}),document.addEventListener("wheel",d,rL),document.addEventListener("touchmove",d,rL),document.addEventListener("touchstart",u,rL),function(){rV=rV.filter(function(e){return e!==s}),document.removeEventListener("wheel",d,rL),document.removeEventListener("touchmove",d,rL),document.removeEventListener("touchstart",u,rL)}},[]);var p=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(s,{styles:"\n  .block-interactivity-".concat(n," {pointer-events: none;}\n  .allow-interactivity-").concat(n," {pointer-events: all;}\n")}):null,p?i.createElement(rO,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},ry.useMedium(a),rw);var rX=i.forwardRef(function(e,t){return i.createElement(rb,rc({},e,{ref:t,sideCar:rY}))});rX.classNames=rb.classNames;var rG=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},rJ=new WeakMap,rZ=new WeakMap,rQ={},r0=0,r1=function(e){return e&&(e.host||r1(e.parentNode))},r2=function(e,t,r,a){var n=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=r1(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});rQ[r]||(rQ[r]=new WeakMap);var s=rQ[r],o=[],i=new Set,l=new Set(n),d=function(e){!e||i.has(e)||(i.add(e),d(e.parentNode))};n.forEach(d);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(i.has(e))c(e);else try{var t=e.getAttribute(a),n=null!==t&&"false"!==t,l=(rJ.get(e)||0)+1,d=(s.get(e)||0)+1;rJ.set(e,l),s.set(e,d),o.push(e),1===l&&n&&rZ.set(e,!0),1===d&&e.setAttribute(r,"true"),n||e.setAttribute(a,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),i.clear(),r0++,function(){o.forEach(function(e){var t=rJ.get(e)-1,n=s.get(e)-1;rJ.set(e,t),s.set(e,n),t||(rZ.has(e)||e.removeAttribute(a),rZ.delete(e)),n||e.removeAttribute(r)}),--r0||(rJ=new WeakMap,rJ=new WeakMap,rZ=new WeakMap,rQ={})}},r5=function(e,t,r){void 0===r&&(r="data-aria-hidden");var a=Array.from(Array.isArray(e)?e:[e]),n=t||rG(e);return n?(a.push.apply(a,Array.from(n.querySelectorAll("[aria-live], script"))),r2(a,n,r,"aria-hidden")):function(){return null}},r4="Dialog",[r3,r6]=tB(r4),[r9,r8]=r3(r4),r7=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:s,modal:l=!0}=e,d=i.useRef(null),c=i.useRef(null),[u,m]=tX({prop:a,defaultProp:n??!1,onChange:s,caller:r4});return(0,o.jsx)(r9,{scope:t,triggerRef:d,contentRef:c,contentId:tV(),titleId:tV(),descriptionId:tV(),open:u,onOpenChange:m,onOpenToggle:i.useCallback(()=>m(e=>!e),[m]),modal:l,children:r})};r7.displayName=r4;var ae="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=r8(ae,r),s=tK(t,n.triggerRef);return(0,o.jsx)(tQ.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":ab(n.open),...a,ref:s,onClick:tF(e.onClick,n.onOpenToggle)})}).displayName=ae;var at="DialogPortal",[ar,aa]=r3(at,{forceMount:void 0}),an=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,s=r8(at,t);return(0,o.jsx)(ar,{scope:t,forceMount:r,children:i.Children.map(a,e=>(0,o.jsx)(ro,{present:r||s.open,children:(0,o.jsx)(rs,{asChild:!0,container:n,children:e})}))})};an.displayName=at;var as="DialogOverlay",ao=i.forwardRef((e,t)=>{let r=aa(as,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=r8(as,e.__scopeDialog);return s.modal?(0,o.jsx)(ro,{present:a||s.open,children:(0,o.jsx)(al,{...n,ref:t})}):null});ao.displayName=as;var ai=tG("DialogOverlay.RemoveScroll"),al=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=r8(as,r);return(0,o.jsx)(rX,{as:ai,allowPinchZoom:!0,shards:[n.contentRef],children:(0,o.jsx)(tQ.div,{"data-state":ab(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),ad="DialogContent",ac=i.forwardRef((e,t)=>{let r=aa(ad,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=r8(ad,e.__scopeDialog);return(0,o.jsx)(ro,{present:a||s.open,children:s.modal?(0,o.jsx)(au,{...n,ref:t}):(0,o.jsx)(am,{...n,ref:t})})});ac.displayName=ad;var au=i.forwardRef((e,t)=>{let r=r8(ad,e.__scopeDialog),a=i.useRef(null),n=tK(t,r.contentRef,a);return i.useEffect(()=>{let e=a.current;if(e)return r5(e)},[]),(0,o.jsx)(af,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:tF(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:tF(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:tF(e.onFocusOutside,e=>e.preventDefault())})}),am=i.forwardRef((e,t)=>{let r=r8(ad,e.__scopeDialog),a=i.useRef(!1),n=i.useRef(!1);return(0,o.jsx)(af,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),af=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:n,onCloseAutoFocus:s,...l}=e,d=r8(ad,r),c=i.useRef(null),u=tK(t,c);return i.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??rd()),document.body.insertAdjacentElement("beforeend",e[1]??rd()),rl++,()=>{1===rl&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),rl--}},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(t7,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:n,onUnmountAutoFocus:s,children:(0,o.jsx)(t5,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":ab(d.open),...l,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(ak,{titleId:d.titleId}),(0,o.jsx)(a_,{contentRef:c,descriptionId:d.descriptionId})]})]})}),ap="DialogTitle",ah=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=r8(ap,r);return(0,o.jsx)(tQ.h2,{id:n.titleId,...a,ref:t})});ah.displayName=ap;var ag="DialogDescription",ax=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=r8(ag,r);return(0,o.jsx)(tQ.p,{id:n.descriptionId,...a,ref:t})});ax.displayName=ag;var ay="DialogClose",av=i.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=r8(ay,r);return(0,o.jsx)(tQ.button,{type:"button",...a,ref:t,onClick:tF(e.onClick,()=>n.onOpenChange(!1))})});function ab(e){return e?"open":"closed"}av.displayName=ay;var aw="DialogTitleWarning",[aj,aN]=function(e,t){let r=i.createContext(t),a=e=>{let{children:t,...a}=e,n=i.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:n,children:t})};return a.displayName=e+"Provider",[a,function(a){let n=i.useContext(r);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}(aw,{contentName:ad,titleName:ap,docsSlug:"dialog"}),ak=({titleId:e})=>{let t=aN(aw),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&document.getElementById(e)},[r,e]),null},a_=({contentRef:e,descriptionId:t})=>{let r=aN("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return i.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&document.getElementById(t)},[a,e,t]),null},aE=r(11860);let aC=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ao,{ref:r,className:`fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ${e||""}`,...t}));aC.displayName=ao.displayName;let aS=i.forwardRef(({className:e,children:t,...r},a)=>(0,o.jsxs)(an,{children:[(0,o.jsx)(aC,{}),(0,o.jsxs)(ac,{ref:a,className:`fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ${e||""}`,...r,children:[t,(0,o.jsxs)(av,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,o.jsx)(aE.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));aS.displayName=ac.displayName;let aA=({className:e,...t})=>(0,o.jsx)("div",{className:`flex flex-col space-y-1.5 text-center sm:text-left ${e||""}`,...t});aA.displayName="DialogHeader";let aR=({className:e,...t})=>(0,o.jsx)("div",{className:`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ${e||""}`,...t});aR.displayName="DialogFooter";let aP=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ah,{ref:r,className:`text-lg font-semibold leading-none tracking-tight ${e||""}`,...t}));aP.displayName=ah.displayName;let aT=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ax,{ref:r,className:`text-sm text-gray-600 ${e||""}`,...t}));aT.displayName=ax.displayName;let aM=(0,i.forwardRef)(({className:e="",label:t,error:r,helperText:a,icon:n,iconPosition:s="left",id:i,...l},d)=>{let c=i||`input-${Math.random().toString(36).substr(2,9)}`;return(0,o.jsxs)("div",{className:"space-y-2",children:[t&&(0,o.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,o.jsxs)("div",{className:"relative",children:[n&&"left"===s&&(0,o.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,o.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})}),(0,o.jsx)("input",{ref:d,id:c,className:`
              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
              focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-all duration-200
              ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
              ${n&&"left"===s?"pl-10":""}
              ${n&&"right"===s?"pr-10":""}
              ${e}
            `,...l}),n&&"right"===s&&(0,o.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,o.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})})]}),r&&(0,o.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,o.jsx)("p",{className:"text-sm text-gray-500",children:a})]})});aM.displayName="Input",(0,i.forwardRef)(({className:e="",label:t,error:r,helperText:a,id:n,...s},i)=>{let l=n||`textarea-${Math.random().toString(36).substr(2,9)}`;return(0,o.jsxs)("div",{className:"space-y-2",children:[t&&(0,o.jsx)("label",{htmlFor:l,className:"block text-sm font-medium text-gray-300",children:t}),(0,o.jsx)("textarea",{ref:i,id:l,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200 resize-none
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...s}),r&&(0,o.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,o.jsx)("p",{className:"text-sm text-gray-500",children:a})]})}).displayName="Textarea",(0,i.forwardRef)(({className:e="",label:t,error:r,helperText:a,options:n=[],children:s,id:i,...l},d)=>{let c=i||`select-${Math.random().toString(36).substr(2,9)}`;return(0,o.jsxs)("div",{className:"space-y-2",children:[t&&(0,o.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,o.jsxs)("select",{ref:d,id:c,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...l,children:[n.map(e=>(0,o.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),s]}),r&&(0,o.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,o.jsx)("p",{className:"text-sm text-gray-500",children:a})]})}).displayName="Select";var aO=i.forwardRef((e,t)=>(0,o.jsx)(tQ.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));aO.displayName="Label";let aI=tr("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),aD=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(aO,{ref:r,className:`${aI()} ${e||""}`,...t}));aD.displayName=aO.displayName;let aL=tr("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),a$=i.forwardRef(({className:e,variant:t,...r},a)=>(0,o.jsx)("div",{ref:a,role:"alert",className:`${aL({variant:t})} ${e||""}`,...r}));a$.displayName="Alert",i.forwardRef(({className:e,...t},r)=>(0,o.jsx)("h5",{ref:r,className:`mb-1 font-medium leading-none tracking-tight ${e||""}`,...t})).displayName="AlertTitle";let aF=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)("div",{ref:r,className:`text-sm [&_p]:leading-relaxed ${e||""}`,...t}));aF.displayName="AlertDescription";let az=(0,ts.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),aW=(0,ts.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),aK=(0,ts.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function aB({open:e,onOpenChange:t,onCreateApiKey:r,configName:a,creating:n,subscriptionTier:s}){let[l,d]=(0,i.useState)("form"),[c,u]=(0,i.useState)(null),[m,f]=(0,i.useState)(!0),[p,h]=(0,i.useState)(!1),[g,x]=(0,i.useState)({key_name:"",expires_at:""}),y=async e=>{if(e.preventDefault(),!g.key_name.trim())return void th.error("Please enter a name for your API key");try{let e=await r({key_name:g.key_name.trim(),expires_at:g.expires_at||void 0});u(e),d("success")}catch(e){}},v=async()=>{if(c?.api_key)try{await navigator.clipboard.writeText(c.api_key),h(!0),th.success("API key copied to clipboard"),setTimeout(()=>h(!1),2e3)}catch(e){th.error("Failed to copy API key")}},b=()=>{"form"===l&&(d("form"),u(null),f(!0),x({key_name:"",expires_at:""}),t(!1))};return"success"===l&&c?(0,o.jsx)(r7,{open:e,onOpenChange:()=>{},modal:!0,children:(0,o.jsxs)(aS,{className:"max-w-lg",children:[(0,o.jsxs)(aA,{className:"text-center space-y-3",children:[(0,o.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,o.jsx)(ti,{className:"h-8 w-8 text-green-600"})}),(0,o.jsx)(aP,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,o.jsx)(aT,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,o.jsxs)("div",{className:"space-y-6 py-4",children:[(0,o.jsxs)(a$,{className:"border-red-200 bg-red-50",children:[(0,o.jsx)(az,{className:"h-4 w-4 text-red-600"}),(0,o.jsxs)(aF,{className:"text-red-800 font-medium",children:[(0,o.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(aD,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,o.jsx)("div",{className:"relative",children:(0,o.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,o.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:m?c.api_key:`${c.key_prefix}_${"*".repeat(28)}${c.api_key.slice(-4)}`}),(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsx)(e3,{variant:"ghost",size:"sm",onClick:()=>f(!m),className:"h-8 w-8 p-0",title:m?"Hide key":"Show key",children:m?(0,o.jsx)(aW,{className:"h-4 w-4"}):(0,o.jsx)(aK,{className:"h-4 w-4"})}),(0,o.jsx)(e3,{variant:"ghost",size:"sm",onClick:v,className:`h-8 w-8 p-0 ${p?"text-green-600":""}`,title:"Copy to clipboard",children:p?(0,o.jsx)("span",{className:"text-xs",children:"✓"}):(0,o.jsx)(tg,{className:"h-4 w-4"})})]})]})}),(0,o.jsx)(e3,{onClick:v,variant:"outline",className:"w-full",disabled:p,children:p?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(tg,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(aD,{className:"text-gray-600",children:"Key Name"}),(0,o.jsx)("p",{className:"font-medium text-gray-900",children:c.key_name})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(aD,{className:"text-gray-600",children:"Created"}),(0,o.jsx)("p",{className:"font-medium text-gray-900",children:new Date(c.created_at).toLocaleString()})]})]})]}),(0,o.jsx)(aR,{className:"pt-6",children:(0,o.jsx)(e3,{onClick:()=>{d("form"),u(null),f(!0),h(!1),x({key_name:"",expires_at:""}),t(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,o.jsx)(r7,{open:e,onOpenChange:b,children:(0,o.jsxs)(aS,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsxs)(aA,{children:[(0,o.jsxs)(aP,{className:"flex items-center gap-2",children:[(0,o.jsx)(ti,{className:"h-5 w-5"}),"Create API Key"]}),(0,o.jsxs)(aT,{children:["Create a new API key for programmatic access to ",a]})]}),(0,o.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{htmlFor:"key_name",children:"API Key Name *"}),(0,o.jsx)(aM,{id:"key_name",value:g.key_name,onChange:e=>x(t=>({...t,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,o.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,o.jsx)(aM,{id:"expires_at",type:"datetime-local",value:g.expires_at,onChange:e=>x(t=>({...t,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,o.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,o.jsxs)(aR,{children:[(0,o.jsx)(e3,{type:"button",variant:"outline",onClick:b,children:"Cancel"}),(0,o.jsx)(e3,{type:"submit",disabled:n,children:n?"Creating...":"Create API Key"})]})]})]})})}var aq="Checkbox",[aH,aU]=tB(aq),[aV,aY]=aH(aq);function aX(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:n,disabled:s,form:l,name:d,onCheckedChange:c,required:u,value:m="on",internal_do_not_use_render:f}=e,[p,h]=tX({prop:r,defaultProp:n??!1,onChange:c,caller:aq}),[g,x]=i.useState(null),[y,v]=i.useState(null),b=i.useRef(!1),w=!g||!!l||!!g.closest("form"),j={checked:p,disabled:s,setChecked:h,control:g,setControl:x,name:d,form:l,value:m,hasConsumerStoppedPropagationRef:b,required:u,defaultChecked:!a5(n)&&n,isFormControl:w,bubbleInput:y,setBubbleInput:v};return(0,o.jsx)(aV,{scope:t,...j,children:"function"==typeof f?f(j):a})}var aG="CheckboxTrigger",aJ=i.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},n)=>{let{control:s,value:l,disabled:d,checked:c,required:u,setControl:m,setChecked:f,hasConsumerStoppedPropagationRef:p,isFormControl:h,bubbleInput:g}=aY(aG,e),x=tK(n,m),y=i.useRef(c);return i.useEffect(()=>{let e=s?.form;if(e){let t=()=>f(y.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,f]),(0,o.jsx)(tQ.button,{type:"button",role:"checkbox","aria-checked":a5(c)?"mixed":c,"aria-required":u,"data-state":a4(c),"data-disabled":d?"":void 0,disabled:d,value:l,...a,ref:x,onKeyDown:tF(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:tF(r,e=>{f(e=>!!a5(e)||!e),g&&h&&(p.current=e.isPropagationStopped(),p.current||e.stopPropagation())})})});aJ.displayName=aG;var aZ=i.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:s,required:i,disabled:l,value:d,onCheckedChange:c,form:u,...m}=e;return(0,o.jsx)(aX,{__scopeCheckbox:r,checked:n,defaultChecked:s,disabled:l,required:i,onCheckedChange:c,name:a,form:u,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(aJ,{...m,ref:t,__scopeCheckbox:r}),e&&(0,o.jsx)(a2,{__scopeCheckbox:r})]})})});aZ.displayName=aq;var aQ="CheckboxIndicator",a0=i.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,s=aY(aQ,r);return(0,o.jsx)(ro,{present:a||a5(s.checked)||!0===s.checked,children:(0,o.jsx)(tQ.span,{"data-state":a4(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});a0.displayName=aQ;var a1="CheckboxBubbleInput",a2=i.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:n,checked:s,defaultChecked:l,required:d,disabled:c,name:u,value:m,form:f,bubbleInput:p,setBubbleInput:h}=aY(a1,e),g=tK(r,h),x=function(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(s),y=function(e){let[t,r]=i.useState(void 0);return tq(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let a,n;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;a=t.inlineSize,n=t.blockSize}else a=e.offsetWidth,n=e.offsetHeight;r({width:a,height:n})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(a);i.useEffect(()=>{if(!p)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!n.current;if(x!==s&&e){let r=new Event("click",{bubbles:t});p.indeterminate=a5(s),e.call(p,!a5(s)&&s),p.dispatchEvent(r)}},[p,x,s,n]);let v=i.useRef(!a5(s)&&s);return(0,o.jsx)(tQ.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??v.current,required:d,disabled:c,name:u,value:m,form:f,...t,tabIndex:-1,ref:g,style:{...t.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function a5(e){return"indeterminate"===e}function a4(e){return a5(e)?"indeterminate":e?"checked":"unchecked"}a2.displayName=a1;let a3=(0,ts.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),a6=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(aZ,{ref:r,className:`peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white data-[state=checked]:border-orange-600 ${e||""}`,...t,children:(0,o.jsx)(a0,{className:"flex items-center justify-center text-current",children:(0,o.jsx)(a3,{className:"h-4 w-4"})})}));a6.displayName=aZ.displayName;var a9="horizontal",a8=["horizontal","vertical"],a7=i.forwardRef((e,t)=>{var r;let{decorative:a,orientation:n=a9,...s}=e,i=(r=n,a8.includes(r))?n:a9;return(0,o.jsx)(tQ.div,{"data-orientation":i,...a?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"},...s,ref:t})});a7.displayName="Separator";let ne=i.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},n)=>(0,o.jsx)(a7,{ref:n,decorative:r,orientation:t,className:`shrink-0 bg-gray-200 ${"horizontal"===t?"h-[1px] w-full":"h-full w-[1px]"} ${e||""}`,...a}));function nt({open:e,onOpenChange:t,apiKey:r,onUpdateApiKey:a}){let[n,s]=(0,i.useState)(!1),[l,d]=(0,i.useState)({key_name:"",permissions:{chat:!0,streaming:!0,all_models:!0},allowed_ips:[],allowed_domains:[],status:"active",expires_at:""}),[c,u]=(0,i.useState)(""),[m,f]=(0,i.useState)(""),p=async e=>{if(e.preventDefault(),!l.key_name.trim())return void th.error("Please enter a name for your API key");try{s(!0),await a({...l,key_name:l.key_name.trim(),expires_at:l.expires_at||null})}catch(e){}finally{s(!1)}},h=()=>{c.trim()&&!l.allowed_ips.includes(c.trim())&&(d(e=>({...e,allowed_ips:[...e.allowed_ips,c.trim()]})),u(""))},g=e=>{d(t=>({...t,allowed_ips:t.allowed_ips.filter(t=>t!==e)}))},x=()=>{m.trim()&&!l.allowed_domains.includes(m.trim())&&(d(e=>({...e,allowed_domains:[...e.allowed_domains,m.trim()]})),f(""))},y=e=>{d(t=>({...t,allowed_domains:t.allowed_domains.filter(t=>t!==e)}))};return(0,o.jsx)(r7,{open:e,onOpenChange:t,children:(0,o.jsxs)(aS,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsxs)(aA,{children:[(0,o.jsxs)(aP,{className:"flex items-center gap-2",children:[(0,o.jsx)(tw,{className:"h-5 w-5"}),"Edit API Key"]}),(0,o.jsx)(aT,{children:"Update the settings for your API key"})]}),(0,o.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{htmlFor:"key_name",children:"API Key Name *"}),(0,o.jsx)(aM,{id:"key_name",value:l.key_name,onChange:e=>d(t=>({...t,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{htmlFor:"status",children:"Status"}),(0,o.jsxs)("select",{id:"status",value:l.status,onChange:e=>d(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,o.jsx)("option",{value:"active",children:"Active"}),(0,o.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,o.jsx)(ne,{}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(tx,{className:"h-4 w-4"}),(0,o.jsx)(aD,{className:"text-base font-semibold",children:"Permissions"})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(a6,{id:"chat",checked:l.permissions.chat,onCheckedChange:e=>d(t=>({...t,permissions:{...t.permissions,chat:!!e}}))}),(0,o.jsx)(aD,{htmlFor:"chat",className:"text-sm",children:"Chat Completions"}),(0,o.jsx)(tn,{variant:"secondary",className:"text-xs",children:"Required"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(a6,{id:"streaming",checked:l.permissions.streaming,onCheckedChange:e=>d(t=>({...t,permissions:{...t.permissions,streaming:!!e}}))}),(0,o.jsx)(aD,{htmlFor:"streaming",className:"text-sm",children:"Streaming Responses"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(a6,{id:"all_models",checked:l.permissions.all_models,onCheckedChange:e=>d(t=>({...t,permissions:{...t.permissions,all_models:!!e}}))}),(0,o.jsx)(aD,{htmlFor:"all_models",className:"text-sm",children:"Access to All Models"})]})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(tx,{className:"h-4 w-4"}),(0,o.jsx)(aD,{className:"text-base font-semibold",children:"Security Restrictions"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{children:"Allowed IP Addresses"}),(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsx)(aM,{value:c,onChange:e=>u(e.target.value),placeholder:"e.g., *********** or 10.0.0.0/8",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),h())}),(0,o.jsx)(e3,{type:"button",onClick:h,size:"sm",children:(0,o.jsx)(tl,{className:"h-4 w-4"})})]}),l.allowed_ips.length>0&&(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:l.allowed_ips.map(e=>(0,o.jsxs)(tn,{variant:"secondary",className:"text-xs",children:[e,(0,o.jsx)(e3,{type:"button",variant:"ghost",size:"sm",onClick:()=>g(e),className:"ml-1 h-3 w-3 p-0",children:(0,o.jsx)(aE.A,{className:"h-2 w-2"})})]},e))})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{children:"Allowed Domains (CORS)"}),(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsx)(aM,{value:m,onChange:e=>f(e.target.value),placeholder:"e.g., example.com or *.example.com",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),x())}),(0,o.jsx)(e3,{type:"button",onClick:x,size:"sm",children:(0,o.jsx)(tl,{className:"h-4 w-4"})})]}),l.allowed_domains.length>0&&(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:l.allowed_domains.map(e=>(0,o.jsxs)(tn,{variant:"secondary",className:"text-xs",children:[(0,o.jsx)(ty,{className:"h-3 w-3 mr-1"}),e,(0,o.jsx)(e3,{type:"button",variant:"ghost",size:"sm",onClick:()=>y(e),className:"ml-1 h-3 w-3 p-0",children:(0,o.jsx)(aE.A,{className:"h-2 w-2"})})]},e))})]})]}),(0,o.jsx)(ne,{}),(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(aD,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,o.jsx)(aM,{id:"expires_at",type:"datetime-local",value:l.expires_at,onChange:e=>d(t=>({...t,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16)})]})}),(0,o.jsxs)(aR,{children:[(0,o.jsx)(e3,{type:"button",variant:"outline",onClick:()=>t(!1),children:"Cancel"}),(0,o.jsx)(e3,{type:"submit",disabled:n,children:n?"Updating...":"Update API Key"})]})]})]})})}ne.displayName=a7.displayName;let nr=(0,ts.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);function na({open:e,onOpenChange:t,apiKey:r}){let[a,n]=(0,i.useState)(!0),[s,l]=(0,i.useState)(null),[d,c]=(0,i.useState)([]),[u,m]=(0,i.useState)("24h"),f=async()=>{if(r?.id)try{n(!0);let e=await fetch(`/api/user-api-keys/${r.id}/usage/stats?range=${u}`);if(e.ok){let t=await e.json();l(t)}let t=await fetch(`/api/user-api-keys/${r.id}/usage/logs?limit=50&range=${u}`);if(t.ok){let e=await t.json();c(e.usage_logs||[])}}catch(e){}finally{n(!1)}},p=e=>e>=200&&e<300?"bg-green-100 text-green-800 border-green-200":e>=400&&e<500?"bg-yellow-100 text-yellow-800 border-yellow-200":e>=500?"bg-red-100 text-red-800 border-red-200":"bg-gray-100 text-gray-800 border-gray-200",h=async()=>{try{let e=await fetch(`/api/user-api-keys/${r.id}/usage/export?range=${u}`);if(e.ok){let t=await e.blob(),a=window.URL.createObjectURL(t),n=document.createElement("a");n.href=a,n.download=`api-key-usage-${r.key_name}-${u}.csv`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(a),document.body.removeChild(n)}}catch(e){}};return a?(0,o.jsx)(r7,{open:e,onOpenChange:t,children:(0,o.jsxs)(aS,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsx)(aA,{children:(0,o.jsxs)(aP,{className:"flex items-center gap-2",children:[(0,o.jsx)(tv,{className:"h-5 w-5"}),"API Key Usage"]})}),(0,o.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,o.jsx)(to,{className:"h-6 w-6 animate-spin mr-2"}),"Loading usage data..."]})]})}):(0,o.jsx)(r7,{open:e,onOpenChange:t,children:(0,o.jsxs)(aS,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,o.jsxs)(aA,{children:[(0,o.jsxs)(aP,{className:"flex items-center gap-2",children:[(0,o.jsx)(tv,{className:"h-5 w-5"}),"API Key Usage: ",r.key_name]}),(0,o.jsx)(aT,{children:"Usage statistics and logs for your API key"})]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(e3,{variant:"24h"===u?"default":"outline",size:"sm",onClick:()=>m("24h"),children:"Last 24 Hours"}),(0,o.jsx)(e3,{variant:"7d"===u?"default":"outline",size:"sm",onClick:()=>m("7d"),children:"Last 7 Days"}),(0,o.jsx)(e3,{variant:"30d"===u?"default":"outline",size:"sm",onClick:()=>m("30d"),children:"Last 30 Days"})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsxs)(e3,{variant:"outline",size:"sm",onClick:f,children:[(0,o.jsx)(to,{className:"h-4 w-4 mr-1"}),"Refresh"]}),(0,o.jsxs)(e3,{variant:"outline",size:"sm",onClick:h,children:[(0,o.jsx)(nr,{className:"h-4 w-4 mr-1"}),"Export"]})]})]}),s&&(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,o.jsxs)(e6,{children:[(0,o.jsx)(e9,{className:"pb-2",children:(0,o.jsx)(e8,{className:"text-sm font-medium text-gray-600",children:"Total Requests"})}),(0,o.jsxs)(e7,{children:[(0,o.jsx)("div",{className:"text-2xl font-bold",children:s.total_requests.toLocaleString()}),(0,o.jsxs)("p",{className:"text-xs text-gray-600",children:[s.requests_today," today"]})]})]}),(0,o.jsxs)(e6,{children:[(0,o.jsx)(e9,{className:"pb-2",children:(0,o.jsx)(e8,{className:"text-sm font-medium text-gray-600",children:"Success Rate"})}),(0,o.jsxs)(e7,{children:[(0,o.jsxs)("div",{className:"text-2xl font-bold",children:[s.success_rate.toFixed(1),"%"]}),(0,o.jsx)("p",{className:"text-xs text-gray-600",children:"HTTP 2xx responses"})]})]}),(0,o.jsxs)(e6,{children:[(0,o.jsx)(e9,{className:"pb-2",children:(0,o.jsx)(e8,{className:"text-sm font-medium text-gray-600",children:"Avg Response Time"})}),(0,o.jsxs)(e7,{children:[(0,o.jsxs)("div",{className:"text-2xl font-bold",children:[s.avg_response_time,"ms"]}),(0,o.jsx)("p",{className:"text-xs text-gray-600",children:"Average latency"})]})]}),(0,o.jsxs)(e6,{children:[(0,o.jsx)(e9,{className:"pb-2",children:(0,o.jsx)(e8,{className:"text-sm font-medium text-gray-600",children:"Total Tokens"})}),(0,o.jsxs)(e7,{children:[(0,o.jsx)("div",{className:"text-2xl font-bold",children:s.total_tokens.toLocaleString()}),(0,o.jsxs)("p",{className:"text-xs text-gray-600",children:["$",s.estimated_cost.toFixed(4)," estimated"]})]})]})]}),(0,o.jsxs)(e6,{children:[(0,o.jsx)(e9,{children:(0,o.jsxs)(e8,{className:"text-lg flex items-center gap-2",children:[(0,o.jsx)(tb,{className:"h-5 w-5"}),"Recent Requests"]})}),(0,o.jsx)(e7,{children:0===d.length?(0,o.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,o.jsx)(td.A,{className:"h-8 w-8 mx-auto mb-2"}),"No usage logs found for the selected time range"]}):(0,o.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:d.map(e=>(0,o.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50",children:[(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)(tn,{className:p(e.status_code),children:e.status_code}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"font-medium text-sm",children:[e.http_method," ",e.endpoint]}),(0,o.jsxs)("div",{className:"text-xs text-gray-600",children:[tL(new Date(e.request_timestamp),{addSuffix:!0}),e.ip_address&&` • ${e.ip_address}`]})]})]}),(0,o.jsxs)("div",{className:"text-right text-xs text-gray-600",children:[e.model_used&&(0,o.jsxs)("div",{children:["Model: ",e.model_used]}),e.response_time_ms&&(0,o.jsxs)("div",{children:[e.response_time_ms,"ms"]}),e.tokens_prompt&&e.tokens_completion&&(0,o.jsxs)("div",{children:[(e.tokens_prompt+e.tokens_completion).toLocaleString()," tokens"]})]})]},e.id))})})]})]})]})})}function nn({configId:e,configName:t}){let[r,a]=(0,i.useState)([]),[n,s]=(0,i.useState)(!0),[l,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!1),[m,f]=(0,i.useState)(!1),[p,h]=(0,i.useState)(!1),[g,x]=(0,i.useState)(null),[y,v]=(0,i.useState)(null),b=async()=>{try{s(!0);let t=await fetch(`/api/user-api-keys?config_id=${e}`);if(!t.ok)throw Error("Failed to fetch API keys");let r=await t.json();a(r.api_keys||[])}catch(e){th.error("Failed to load API keys")}finally{s(!1)}},w=async t=>{try{d(!0);let r=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...t,custom_api_config_id:e})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to create API key")}let a=await r.json();return th.success("API key created successfully!"),a}catch(e){throw th.error(e.message||"Failed to create API key"),e}finally{d(!1)}},j=async(e,t)=>{try{let r=await fetch(`/api/user-api-keys/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to update API key")}th.success("API key updated successfully"),await b(),f(!1),x(null)}catch(e){th.error(e.message||"Failed to update API key")}},N=async e=>{if(confirm("Are you sure you want to revoke this API key? This action cannot be undone."))try{let t=await fetch(`/api/user-api-keys/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to revoke API key")}th.success("API key revoked successfully"),await b()}catch(e){th.error(e.message||"Failed to revoke API key")}},k=e=>{x(r.find(t=>t.id===e)),h(!0)},_=!y||y.currentCount<y.keyLimit;return n?(0,o.jsx)(e6,{children:(0,o.jsxs)(e7,{className:"flex items-center justify-center py-8",children:[(0,o.jsx)(to,{className:"h-6 w-6 animate-spin mr-2"}),"Loading API keys..."]})}):(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,o.jsx)(ti,{className:"h-6 w-6"}),"API Keys"]}),(0,o.jsxs)("p",{className:"text-gray-600 mt-1",children:["Generate API keys for programmatic access to ",t]})]}),(0,o.jsxs)(e3,{onClick:()=>u(!0),disabled:!_,className:"flex items-center gap-2",children:[(0,o.jsx)(tl,{className:"h-4 w-4"}),"Create API Key"]})]}),y&&(0,o.jsxs)(a$,{children:[(0,o.jsx)(td.A,{className:"h-4 w-4"}),(0,o.jsxs)(aF,{children:["You are on the ",(0,o.jsx)(tn,{variant:"secondary",children:y.tier})," plan. You have used ",y.currentCount," of ",y.keyLimit," API keys.",!_&&(0,o.jsx)("span",{className:"text-red-600 ml-2",children:"Upgrade your plan to create more API keys."})]})]}),0===r.length?(0,o.jsx)(e6,{children:(0,o.jsxs)(e7,{className:"text-center py-8",children:[(0,o.jsx)(ti,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No API Keys"}),(0,o.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),(0,o.jsxs)(e3,{onClick:()=>u(!0),disabled:!_,children:[(0,o.jsx)(tl,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]})]})}):(0,o.jsx)("div",{className:"grid gap-4",children:r.map(e=>(0,o.jsx)(t$,{apiKey:e,onEdit:e=>{x(e),f(!0)},onRevoke:N,onViewUsage:k},e.id))}),(0,o.jsx)(aB,{open:c,onOpenChange:e=>{u(e),e||b()},onCreateApiKey:w,configName:t,creating:l,subscriptionTier:y?.tier||"starter"}),g&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(nt,{open:m,onOpenChange:f,apiKey:g,onUpdateApiKey:e=>j(g.id,e)}),(0,o.jsx)(na,{open:p,onOpenChange:h,apiKey:g})]})]})}let ns=c.MG.map(e=>({value:e.id,label:e.name}));function no(){let e=(0,d.useParams)().configId,t=(0,eZ.Z)(),r=(0,e5.bu)(),a=r?.navigateOptimistically||(e=>{window.location.href=e}),{getCachedData:n,isCached:s}=(0,eQ._)(),{createHoverPrefetch:l}=(0,e2.c)(),[E,C]=(0,i.useState)(null),[S,A]=(0,i.useState)(!0),[R,P]=(0,i.useState)(!1),[T,M]=(0,i.useState)(ns[0]?.value||"openai"),[O,I]=(0,i.useState)(""),[D,L]=(0,i.useState)(""),[$,F]=(0,i.useState)(""),[z,W]=(0,i.useState)(1),[K,B]=(0,i.useState)(!1),[q,H]=(0,i.useState)(null),[U,V]=(0,i.useState)(null),[Y,X]=(0,i.useState)(null),[G,J]=(0,i.useState)(!1),[Z,Q]=(0,i.useState)(null),[ee,et]=(0,i.useState)([]),[er,ea]=(0,i.useState)(!0),[en,es]=(0,i.useState)(null),[eo,ei]=(0,i.useState)(null),[el,ed]=(0,i.useState)(null),[ec,eu]=(0,i.useState)(null),[em,ef]=(0,i.useState)(1),[ep,eh]=(0,i.useState)(""),[eg,ex]=(0,i.useState)(!1),[ey,ev]=(0,i.useState)([]),[eb,ew]=(0,i.useState)(!1),[ej,eN]=(0,i.useState)(null),[ek,e_]=(0,i.useState)(!1),[eE,eC]=(0,i.useState)(""),[eS,eA]=(0,i.useState)(""),[eR,eP]=(0,i.useState)(""),[eT,eM]=(0,i.useState)(!1),[eO,eI]=(0,i.useState)(null),[eD,eL]=(0,i.useState)(null),[e$,eF]=(0,i.useState)("provider-keys");(0,i.useCallback)(async()=>{if(!e)return;let t=n(e);if(t&&t.configDetails){C(t.configDetails),A(!1);return}s(e)||P(!0),A(!0),H(null);try{let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations list")}let r=(await t.json()).find(t=>t.id===e);if(!r)throw Error("Configuration not found in the list.");C(r)}catch(e){H(`Error loading model configuration: ${e.message}`),C(null)}finally{A(!1),P(!1)}},[e,n,s]),(0,i.useCallback)(async()=>{let t=n(e);if(t&&t.models){X(t.models),J(!1);return}J(!0),Q(null),X(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?X(t.models):X([])}catch(e){Q(`Error fetching models: ${e.message}`),X([])}finally{J(!1)}},[e,n]);let ez=(0,i.useCallback)(async()=>{let t=n(e);if(t&&t.userCustomRoles){ev(t.userCustomRoles),ew(!1);return}ew(!0),eN(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();ev(t)}else{let t;try{t=await e.json()}catch(r){t={error:await e.text().catch(()=>`HTTP error ${e.status}`)}}let r=t.error||(t.issues?JSON.stringify(t.issues):`Failed to fetch custom roles (status: ${e.status})`);if(401===e.status)eN(r);else throw Error(r);ev([])}}catch(e){eN(e.message),ev([])}finally{ew(!1)}},[]),eW=(0,i.useCallback)(async()=>{if(!e||!ey)return;let t=n(e);if(t&&t.apiKeys&&void 0!==t.defaultChatKeyId){let e=t.apiKeys.map(async e=>{let r=await fetch(`/api/keys/${e.id}/roles`),a=[];return r.ok&&(a=(await r.json()).map(e=>{let t=m(e.role_name);if(t)return t;let r=ey.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:t.defaultChatKeyId===e.id}});et(await Promise.all(e)),ei(t.defaultChatKeyId),ea(!1);return}ea(!0),H(e=>e&&e.startsWith("Error loading model configuration:")?e:null),V(null);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch API keys")}let r=await t.json(),a=await fetch(`/api/custom-configs/${e}/default-chat-key`);a.ok;let n=200===a.status?await a.json():null;ei(n?.id||null);let s=r.map(async e=>{let t=await fetch(`/api/keys/${e.id}/roles`),r=[];return t.ok&&(r=(await t.json()).map(e=>{let t=m(e.role_name);if(t)return t;let r=ey.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:r,is_default_general_chat_model:n?.id===e.id}}),o=await Promise.all(s);et(o)}catch(e){H(t=>t?`${t}; ${e.message}`:e.message)}finally{ea(!1)}},[e,ey]),eK=(0,i.useMemo)(()=>{if(Y){let e=c.MG.find(e=>e.id===T);if(!e)return[];if("openrouter"===e.id)return Y.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return Y.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),Y.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return Y.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[Y,T]),eB=(0,i.useMemo)(()=>{if(Y&&ec){let e=c.MG.find(e=>e.id===ec.provider);if(!e)return[];if("openrouter"===e.id)return Y.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return Y.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),Y.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return Y.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[Y,ec]),eq=async t=>{if(t.preventDefault(),!e)return void H("Configuration ID is missing.");if(ee.some(e=>e.predefined_model_id===O))return void H("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");B(!0),H(null),V(null);let r=[...ee];try{let t=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:e,provider:T,predefined_model_id:O,api_key_raw:D,label:$,temperature:z})}),r=await t.json();if(!t.ok)throw Error(r.details||r.error||"Failed to save API key");let a={id:r.id,custom_api_config_id:e,provider:T,predefined_model_id:O,label:$,temperature:z,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};et(e=>[...e,a]),V(`API key "${$}" saved successfully!`),M(ns[0]?.value||"openai"),L(""),F(""),W(1),eK.length>0&&I(eK[0].value)}catch(e){et(r),H(`Save Key Error: ${e.message}`)}finally{B(!1)}},eH=e=>{eu(e),ef(e.temperature||1),eh(e.predefined_model_id)},eU=async()=>{if(!ec)return;if(ee.some(e=>e.id!==ec.id&&e.predefined_model_id===ep))return void H("This model is already configured in this setup. Each model can only be used once per configuration.");ex(!0),H(null),V(null);let e=[...ee];et(e=>e.map(e=>e.id===ec.id?{...e,temperature:em,predefined_model_id:ep}:e));try{let t=await fetch(`/api/keys?id=${ec.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:em,predefined_model_id:ep})}),r=await t.json();if(!t.ok)throw et(e),Error(r.details||r.error||"Failed to update API key");V(`API key "${ec.label}" updated successfully!`),eu(null)}catch(e){H(`Update Key Error: ${e.message}`)}finally{ex(!1)}},eV=(e,r)=>{t.showConfirmation({title:"Delete API Key",message:`Are you sure you want to delete the API key "${r}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{es(e),H(null),V(null);let t=[...ee],a=ee.find(t=>t.id===e);et(t=>t.filter(t=>t.id!==e)),a?.is_default_general_chat_model&&ei(null);try{let a=await fetch(`/api/keys/${e}`,{method:"DELETE"}),n=await a.json();if(!a.ok){if(et(t),ei(eo),404===a.status){et(t=>t.filter(t=>t.id!==e)),V(`API key "${r}" was already deleted.`);return}throw Error(n.details||n.error||"Failed to delete API key")}V(`API key "${r}" deleted successfully!`)}catch(e){throw H(`Delete Key Error: ${e.message}`),e}finally{es(null)}})},eY=async t=>{if(!e)return;H(null),V(null);let r=[...ee];et(e=>e.map(e=>({...e,is_default_general_chat_model:e.id===t}))),ei(t);try{let a=await fetch(`/api/custom-configs/${e}/default-key-handler/${t}`,{method:"PUT"}),n=await a.json();if(!a.ok)throw et(r.map(e=>({...e}))),ei(eo),Error(n.details||n.error||"Failed to set default chat key");V(n.message||"Default general chat key updated!")}catch(e){H(`Set Default Error: ${e.message}`)}},eX=async(e,t,r)=>{H(null),V(null);let a=`/api/keys/${e.id}/roles`,n=[...u.map(e=>({...e,isCustom:!1})),...ey.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},s=ee.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),o=null;el&&el.id===e.id&&(o={...el,assigned_roles:[...el.assigned_roles.map(e=>({...e}))]}),et(a=>a.map(a=>{if(a.id===e.id){let e=r?a.assigned_roles.filter(e=>e.id!==t):[...a.assigned_roles,n];return{...a,assigned_roles:e}}return a})),el&&el.id===e.id&&ed(e=>{if(!e)return null;let a=r?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,n];return{...e,assigned_roles:a}});try{let i;i=r?await fetch(`${a}/${t}`,{method:"DELETE"}):await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let l=await i.json();if(!i.ok){if(et(s),o)ed(o);else if(el&&el.id===e.id){let t=s.find(t=>t.id===e.id);t&&ed(t)}let t=409===i.status&&l.error?l.error:l.details||l.error||(r?"Failed to unassign role":"Failed to assign role");throw Error(t)}V(l.message||`Role '${n.name}' ${r?"unassigned":"assigned"} successfully.`)}catch(e){H(`Role Update Error: ${e.message}`)}},e4=async()=>{if(!eE.trim()||eE.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eE.trim()))return void eI("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(u.some(e=>e.id.toLowerCase()===eE.trim().toLowerCase())||ey.some(e=>e.role_id.toLowerCase()===eE.trim().toLowerCase()))return void eI("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eS.trim())return void eI("Role Name is required.");eI(null),eM(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eE.trim(),name:eS.trim(),description:eR.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(a){let r=await e.text().catch(()=>`HTTP status ${e.status}`);t={error:"Server error, could not parse response.",details:r}}let r=t.error||"Failed to create custom role.";if(t.details)r+=` (Details: ${t.details})`;else if(t.issues){let e=Object.entries(t.issues).map(([e,t])=>`${e}: ${t.join(", ")}`).join("; ");r+=` (Issues: ${e})`}throw Error(r)}let t=await e.json();eC(""),eA(""),eP(""),ez(),V(`Custom role '${t.name}' created successfully! It is now available globally.`)}catch(e){eI(e.message)}finally{eM(!1)}},e3=(r,a)=>{r&&t.showConfirmation({title:"Delete Custom Role",message:`Are you sure you want to delete the custom role "${a}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eL(r),eN(null),eI(null),V(null);try{let t=await fetch(`/api/user/custom-roles/${r}`,{method:"DELETE"}),n=await t.json();if(!t.ok)throw Error(n.error||"Failed to delete custom role");ev(e=>e.filter(e=>e.id!==r)),V(n.message||`Global custom role "${a}" deleted successfully.`),e&&eW()}catch(e){throw eN(`Error deleting role: ${e.message}`),e}finally{eL(null)}})};return R&&!s(e)?(0,o.jsx)(e0,{}):S&&!E?(0,o.jsx)(e1,{}):(0,o.jsxs)("div",{className:"min-h-screen",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsxs)("button",{onClick:()=>a("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,o.jsx)(x.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,o.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,o.jsx)("div",{className:"flex-1",children:E?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"flex items-center mb-4",children:[(0,o.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,o.jsx)(h.A,{className:"h-6 w-6 text-white"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:E.name}),(0,o.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),(0,o.jsxs)("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[(0,o.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",E.id]})]}):q&&!S?(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:(0,o.jsx)(f,{className:"h-6 w-6 text-red-600"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),(0,o.jsx)("p",{className:"text-red-500 mt-1",children:q.replace("Error loading model configuration: ","")})]})]}):(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,o.jsx)(y,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),(0,o.jsx)("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),E&&(0,o.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,o.jsxs)("button",{onClick:()=>a(`/routing-setup/${e}?from=model-config`),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...l(e),children:[(0,o.jsx)(h.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),U&&(0,o.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)(v.A,{className:"h-5 w-5 text-green-600"}),(0,o.jsx)("p",{className:"text-green-800 font-medium",children:U})]})}),q&&(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)(f,{className:"h-5 w-5 text-red-600"}),(0,o.jsx)("p",{className:"text-red-800 font-medium",children:q})]})})]}),E&&(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-2",children:(0,o.jsxs)("div",{className:"flex space-x-1",children:[(0,o.jsx)("button",{onClick:()=>eF("provider-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${"provider-keys"===e$?"bg-orange-500 text-white shadow-md":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,o.jsx)(b.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{children:"Provider API Keys"})]})}),(0,o.jsx)("button",{onClick:()=>eF("user-api-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${"user-api-keys"===e$?"bg-orange-500 text-white shadow-md":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"}`,children:(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,o.jsx)(w,{className:"h-4 w-4"}),(0,o.jsx)("span",{children:"Generated API Keys"})]})})]})}),"provider-keys"===e$&&(0,o.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,o.jsx)("div",{className:"xl:col-span-2",children:(0,o.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[(0,o.jsxs)("div",{className:"flex items-center mb-6",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,o.jsx)(j.A,{className:"h-5 w-5 text-white"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add Provider API Key"}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:"Configure new provider key"})]})]}),(0,o.jsxs)("form",{onSubmit:eq,className:"space-y-5",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,o.jsx)("select",{id:"provider",value:T,onChange:e=>{M(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:ns.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),(0,o.jsx)("input",{id:"apiKeyRaw",type:"password",value:D,onChange:e=>L(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),G&&null===Y&&(0,o.jsxs)("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[(0,o.jsx)(y,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),Z&&(0,o.jsx)("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:Z})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),(0,o.jsx)("select",{id:"predefinedModelId",value:O,onChange:e=>I(e.target.value),disabled:!eK.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:eK.length>0?eK.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value)):(0,o.jsx)("option",{value:"",disabled:!0,children:null===Y&&G?"Loading models...":"Select a provider first"})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),(0,o.jsx)("input",{type:"text",id:"label",value:$,onChange:e=>F(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",(0,o.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:z,onChange:e=>W(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"Conservative"}),(0,o.jsx)("div",{className:"flex items-center space-x-2",children:(0,o.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:z,onChange:e=>W(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),(0,o.jsx)("span",{className:"text-xs text-gray-500",children:"Creative"})]}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,o.jsx)("button",{type:"submit",disabled:K||!O||""===O||!D.trim()||!$.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:K?(0,o.jsxs)("span",{className:"flex items-center justify-center",children:[(0,o.jsx)(y,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,o.jsxs)("span",{className:"flex items-center justify-center",children:[(0,o.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,o.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,o.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,o.jsx)(N,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),(0,o.jsxs)("div",{className:"text-xs text-blue-800 space-y-1",children:[(0,o.jsxs)("p",{children:["✅ ",(0,o.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,o.jsxs)("p",{children:["✅ ",(0,o.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,o.jsxs)("p",{children:["❌ ",(0,o.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,o.jsx)("div",{className:"xl:col-span-3",children:(0,o.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[(0,o.jsxs)("div",{className:"flex items-center mb-6",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,o.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),er&&(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)(y,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!er&&0===ee.length&&(!q||q&&q.startsWith("Error loading model configuration:"))&&(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,o.jsx)(b.A,{className:"h-6 w-6 text-gray-400"})}),(0,o.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!er&&ee.length>0&&(0,o.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:ee.map((e,t)=>(0,o.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:(0,o.jsxs)("div",{className:"flex items-start justify-between",children:[(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsxs)("div",{className:"flex items-center mb-2",children:[(0,o.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,o.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[(0,o.jsx)(k.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsxs)("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),(0,o.jsxs)("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),(0,o.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,o.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):(0,o.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_general_chat_model&&(0,o.jsx)("button",{onClick:()=>eY(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,o.jsx)("button",{onClick:()=>eH(e),disabled:en===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,o.jsx)(_.A,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>ed(e),disabled:en===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,o.jsx)(h.A,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>eV(e.id,e.label),disabled:en===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:en===e.id?(0,o.jsx)(g.A,{className:"h-4 w-4 animate-pulse"}):(0,o.jsx)(g.A,{className:"h-4 w-4"})})]})]})},e.id))}),!er&&q&&!q.startsWith("Error loading model configuration:")&&(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)(f,{className:"h-5 w-5 text-red-600"}),(0,o.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",q]})]})})]})})]}),"user-api-keys"===e$&&(0,o.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:(0,o.jsx)(nn,{configId:e,configName:E.name})})]}),el&&(()=>{if(!el)return null;let e=[...u.map(e=>({...e,isCustom:!1})),...ey.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,o.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,o.jsxs)("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",(0,o.jsx)("span",{className:"text-orange-600",children:el.label})]}),(0,o.jsx)("button",{onClick:()=>{ed(null),e_(!1),eI(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,o.jsx)(f,{className:"h-6 w-6"})})]}),(0,o.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[ej&&(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,o.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",ej]})}),(0,o.jsx)("div",{className:"flex justify-end mb-4",children:(0,o.jsxs)("button",{onClick:()=>e_(!ek),className:"btn-primary text-sm inline-flex items-center",children:[(0,o.jsx)(p.A,{className:"h-4 w-4 mr-2"}),ek?"Cancel New Role":"Create New Custom Role"]})}),ek&&(0,o.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[(0,o.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,o.jsx)("input",{type:"text",id:"newCustomRoleId",value:eE,onChange:e=>eC(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),(0,o.jsx)("input",{type:"text",id:"newCustomRoleName",value:eS,onChange:e=>eA(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (optional, max 500 chars)"}),(0,o.jsx)("textarea",{id:"newCustomRoleDescription",value:eR,onChange:e=>eP(e.target.value),rows:2,className:"form-input",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eO&&(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,o.jsx)("p",{className:"text-red-800 text-sm",children:eO})}),(0,o.jsx)("button",{onClick:e4,disabled:eT,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eT?"Saving Role...":"Save Custom Role"})]})]})]}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),(0,o.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eb&&(0,o.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=el.assigned_roles.some(t=>t.id===e.id);return(0,o.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${t?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"}`,children:[(0,o.jsxs)("label",{htmlFor:`role-${e.id}`,className:"flex items-center cursor-pointer flex-grow",children:[(0,o.jsx)("input",{type:"checkbox",id:`role-${e.id}`,checked:t,onChange:()=>eX(el,e.id,t),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),(0,o.jsx)("span",{className:`ml-3 text-sm font-medium ${t?"text-orange-800":"text-gray-900"}`,children:e.name}),e.isCustom&&(0,o.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,o.jsx)("button",{onClick:()=>e3(e.databaseId,e.name),disabled:eD===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eD===e.databaseId?(0,o.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,o.jsx)(g.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,o.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsx)("button",{onClick:()=>{ed(null),e_(!1),eI(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ec&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,o.jsxs)("div",{className:"card w-full max-w-lg",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),(0,o.jsx)("button",{onClick:()=>eu(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,o.jsx)(f,{className:"h-6 w-6"})})]}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:ec.label}),(0,o.jsxs)("p",{className:"text-sm text-gray-600",children:["Current: ",ec.provider," (",ec.predefined_model_id,")"]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,o.jsx)("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:c.MG.find(e=>e.id===ec.provider)?.name||ec.provider}),(0,o.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),(0,o.jsx)("select",{id:"editModelId",value:ep,onChange:e=>eh(e.target.value),disabled:!eB.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:eB.length>0?eB.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value)):(0,o.jsx)("option",{value:"",disabled:!0,children:G?"Loading models...":"No models available"})})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",em]}),(0,o.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:em,onChange:e=>ef(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,o.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,o.jsx)("span",{children:"0.0 (Focused)"}),(0,o.jsx)("span",{children:"1.0 (Balanced)"}),(0,o.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,o.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,o.jsx)("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,o.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,o.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,o.jsx)("button",{onClick:()=>eu(null),className:"btn-secondary",disabled:eg,children:"Cancel"}),(0,o.jsx)("button",{onClick:eU,disabled:eg,className:"btn-primary",children:eg?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!E&&!S&&!q&&(0,o.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,o.jsx)(N,{className:"h-8 w-8 text-gray-400"})}),(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,o.jsxs)("button",{onClick:()=>a("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,o.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,o.jsx)(eJ.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading}),(0,o.jsx)(eG,{id:"global-tooltip"})]})}},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},51983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),n=r(48088),s=r(88170),o=r.n(s),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["my-models",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20218)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/my-models/[configId]/page",pathname:"/my-models/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58089:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var a=r(43210);let n={};function s(){let[e,t]=(0,a.useState)({}),r=(0,a.useRef)({}),s=(0,a.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),o=(0,a.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),i=(0,a.useCallback)(async(e,a="medium")=>{if(s(e))return o(e);if(n[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let i=new AbortController;r.current[e]=i,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===a?await new Promise(e=>setTimeout(e,200)):"medium"===a&&await new Promise(e=>setTimeout(e,50));let[r,s,o]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:i.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:i.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:i.signal})]),l=null,d=[],c="none",u={},m=[];"fulfilled"===r.status&&r.value.ok&&(c=(l=await r.value.json()).routing_strategy||"none",u=l.routing_strategy_params||{}),"fulfilled"===s.status&&s.value.ok&&(d=await s.value.json()),"fulfilled"===o.status&&o.value.ok&&(m=await o.value.json());let f={configDetails:l,apiKeys:d,routingStrategy:c,routingParams:u,complexityAssignments:m};return n[e]={data:f,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),f}catch(r){if("AbortError"===r.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[s,o]),l=(0,a.useCallback)(e=>({onMouseEnter:()=>{s(e)||i(e,"high")}}),[i,s]),d=(0,a.useCallback)(e=>{delete n[e],t(t=>{let r={...t};return delete r[e],r})},[]),c=(0,a.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:i,getCachedData:o,isCached:s,createHoverPrefetch:l,clearCache:d,clearAllCache:c,getStatus:(0,a.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,a.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(43210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:s="",children:o,iconNode:c,...u},m)=>(0,a.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:i("lucide",s),...!o&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(o)?o:[o]])),u=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...s},l)=>(0,a.createElement)(c,{ref:l,iconNode:t,className:i(`lucide-${n(o(e))}`,`lucide-${e}`,r),...s}));return r.displayName=o(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>a});let a=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},69662:(e,t)=>{var r;!function(){"use strict";var a={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=s(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)a.call(e,r)&&e[r]&&(t=s(t,r));return t}(r)))}return e}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(r=(function(){return n}).apply(t,[]))||(e.exports=r)}()},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86099:(e,t,r)=>{Promise.resolve().then(r.bind(r,20218))},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7482,4912,8834],()=>r(51983));module.exports=a})();