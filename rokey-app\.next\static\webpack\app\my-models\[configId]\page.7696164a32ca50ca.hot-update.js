"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx":
/*!**********************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyUsageDialog.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyUsageDialog: () => (/* binding */ ApiKeyUsageDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Calendar,Download,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Calendar,Download,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Calendar,Download,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Calendar,Download,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,Calendar,Download,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyUsageDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ApiKeyUsageDialog(param) {\n    let { open, onOpenChange, apiKey } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [usageStats, setUsageStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [usageLogs, setUsageLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('24h');\n    const fetchUsageData = async ()=>{\n        if (!(apiKey === null || apiKey === void 0 ? void 0 : apiKey.id)) return;\n        try {\n            setLoading(true);\n            // Fetch usage statistics\n            const statsResponse = await fetch(\"/api/user-api-keys/\".concat(apiKey.id, \"/usage/stats?range=\").concat(timeRange));\n            if (statsResponse.ok) {\n                const stats = await statsResponse.json();\n                setUsageStats(stats);\n            }\n            // Fetch recent usage logs\n            const logsResponse = await fetch(\"/api/user-api-keys/\".concat(apiKey.id, \"/usage/logs?limit=50&range=\").concat(timeRange));\n            if (logsResponse.ok) {\n                const logs = await logsResponse.json();\n                setUsageLogs(logs.usage_logs || []);\n            }\n        } catch (error) {\n            console.error('Error fetching usage data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyUsageDialog.useEffect\": ()=>{\n            if (open && (apiKey === null || apiKey === void 0 ? void 0 : apiKey.id)) {\n                fetchUsageData();\n            }\n        }\n    }[\"ApiKeyUsageDialog.useEffect\"], [\n        open,\n        apiKey === null || apiKey === void 0 ? void 0 : apiKey.id,\n        timeRange\n    ]);\n    const getStatusColor = (statusCode)=>{\n        if (statusCode >= 200 && statusCode < 300) return 'text-green-600';\n        if (statusCode >= 400 && statusCode < 500) return 'text-yellow-600';\n        if (statusCode >= 500) return 'text-red-600';\n        return 'text-gray-600';\n    };\n    const getStatusBadgeColor = (statusCode)=>{\n        if (statusCode >= 200 && statusCode < 300) return 'bg-green-100 text-green-800 border-green-200';\n        if (statusCode >= 400 && statusCode < 500) return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        if (statusCode >= 500) return 'bg-red-100 text-red-800 border-red-200';\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    };\n    const exportUsageData = async ()=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKey.id, \"/usage/export?range=\").concat(timeRange));\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.href = url;\n                a.download = \"api-key-usage-\".concat(apiKey.key_name, \"-\").concat(timeRange, \".csv\");\n                document.body.appendChild(a);\n                a.click();\n                window.URL.revokeObjectURL(url);\n                document.body.removeChild(a);\n            }\n        } catch (error) {\n            console.error('Error exporting usage data:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n            open: open,\n            onOpenChange: onOpenChange,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                \"API Key Usage\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 animate-spin mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            \"Loading usage data...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-6xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                \"API Key Usage: \",\n                                apiKey.key_name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: \"Usage statistics and logs for your API key\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: timeRange === '24h' ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setTimeRange('24h'),\n                                            children: \"Last 24 Hours\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: timeRange === '7d' ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setTimeRange('7d'),\n                                            children: \"Last 7 Days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: timeRange === '30d' ? 'default' : 'outline',\n                                            size: \"sm\",\n                                            onClick: ()=>setTimeRange('30d'),\n                                            children: \"Last 30 Days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: fetchUsageData,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: exportUsageData,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Export\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        usageStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Requests\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: usageStats.total_requests.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: [\n                                                        usageStats.requests_today,\n                                                        \" today\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Success Rate\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: [\n                                                        usageStats.success_rate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"HTTP 2xx responses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Avg Response Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: [\n                                                        usageStats.avg_response_time,\n                                                        \"ms\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: \"Average latency\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: usageStats.total_tokens.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        usageStats.estimated_cost.toFixed(4),\n                                                        \" estimated\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                        className: \"text-lg flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Recent Requests\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: usageLogs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_Calendar_Download_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"No usage logs found for the selected time range\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: usageLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                className: getStatusBadgeColor(log.status_code),\n                                                                children: log.status_code\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm\",\n                                                                        children: [\n                                                                            log.http_method,\n                                                                            \" \",\n                                                                            log.endpoint\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: [\n                                                                            (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_11__.formatDistanceToNow)(new Date(log.request_timestamp), {\n                                                                                addSuffix: true\n                                                                            }),\n                                                                            log.ip_address && \" • \".concat(log.ip_address)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right text-xs text-gray-600\",\n                                                        children: [\n                                                            log.model_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    \"Model: \",\n                                                                    log.model_used\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            log.response_time_ms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    log.response_time_ms,\n                                                                    \"ms\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            log.tokens_prompt && log.tokens_completion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    (log.tokens_prompt + log.tokens_completion).toLocaleString(),\n                                                                    \" tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, log.id, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyUsageDialog.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyUsageDialog, \"mxxRU6Bna/r50c73hVwb8jHlz+Y=\");\n_c = ApiKeyUsageDialog;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyUsageDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\n"));

/***/ })

});