"use strict";(()=>{var e={};e.id=4454,e.ids=[4454],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},95392:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>x,serverHooks:()=>g,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{OPTIONS:()=>m,POST:()=>d});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),p=t(49859),l=t(45697);let u=l.z.object({model:l.z.string().optional().default("gpt-3.5-turbo"),messages:l.z.array(l.z.object({role:l.z.enum(["user","assistant","system"]),content:l.z.union([l.z.string(),l.z.array(l.z.any())])})).min(1,{message:"Messages array cannot be empty."}),stream:l.z.boolean().optional().default(!1),temperature:l.z.number().min(0).max(2).optional(),max_tokens:l.z.number().int().positive().optional(),top_p:l.z.number().min(0).max(1).optional(),frequency_penalty:l.z.number().min(-2).max(2).optional(),presence_penalty:l.z.number().min(-2).max(2).optional(),stop:l.z.union([l.z.string(),l.z.array(l.z.string())]).optional(),n:l.z.number().int().positive().optional().default(1),role:l.z.string().optional()}).catchall(l.z.any()),c=new p.S;async function d(e){try{let r=await c.authenticateRequest(e);if(!r.success)return i.NextResponse.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:t,userConfig:s,ipAddress:o}=r;if(!c.hasPermission(t,"chat"))return i.NextResponse.json({error:{message:"API key does not have chat permission",type:"permission_error",code:"insufficient_permissions"}},{status:403});let n=await e.json(),a=u.safeParse(n);if(!a.success)return i.NextResponse.json({error:{message:"Invalid request body",type:"invalid_request_error",code:"invalid_request",details:a.error.flatten().fieldErrors}},{status:400});let p=a.data;if(p.stream&&!c.hasPermission(t,"streaming"))return i.NextResponse.json({error:{message:"API key does not have streaming permission",type:"permission_error",code:"streaming_not_allowed"}},{status:403});let l={custom_api_config_id:s.id,messages:p.messages,stream:p.stream,temperature:p.temperature,max_tokens:p.max_tokens,role:p.role,model:p.model,top_p:p.top_p,frequency_penalty:p.frequency_penalty,presence_penalty:p.presence_penalty,stop:p.stop,n:p.n,_internal_user_id:s.user_id},d=new URL("/api/v1/chat/completions",e.url),m=await fetch(d.toString(),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Forwarded-For":o||"","X-User-API-Key-ID":t.id,"X-External-Request":"true"},body:JSON.stringify(l)});if(c.logApiUsage(t,e,{statusCode:m.status,modelUsed:p.model},o).catch(e=>{}),p.stream)return new Response(m.body,{status:m.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});{let e=await m.json();if(m.ok)return i.NextResponse.json(e,{status:m.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});return i.NextResponse.json({error:{message:e.error||"Internal server error",type:"server_error",code:"internal_error"}},{status:m.status})}}catch(e){return i.NextResponse.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function m(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/external/v1/chat/completions/route",pathname:"/api/external/v1/chat/completions",filename:"route",bundlePath:"app/api/external/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:h,serverHooks:g}=x;function _(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:h})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,5697,6485],()=>t(95392));module.exports=s})();