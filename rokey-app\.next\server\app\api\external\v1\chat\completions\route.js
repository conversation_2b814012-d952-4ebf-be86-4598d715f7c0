(()=>{var e={};e.id=4454,e.ids=[4454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32801:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var s=r(55511),a=r.n(s),i=r(56534);class o{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static generateApiKey(){let e=a().randomBytes(this.RANDOM_PART_LENGTH/2).toString("hex"),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,s=`${r}_${t}`,i=this.hashApiKey(s);return{fullKey:s,prefix:r,secretPart:t,hash:i}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let s=0;s<e;s++){let e=a().randomInt(0,t.length);r+=t[e]}return r}static hashApiKey(e){return a().createHash("sha256").update(e).digest("hex")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static encryptSuffix(e){let t=e.slice(-4);return(0,i.w)(t)}static decryptSuffix(e){try{return(0,i.Y)(e)}catch(e){return"xxxx"}}static createMaskedKey(e,t){let r=this.decryptSuffix(t),s=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(s)}${r}`}static validateSubscriptionLimits(e,t){let r={starter:5,professional:25,enterprise:100},s=r[e]||r.starter;return t>=s?{allowed:!1,limit:s,message:`You have reached the maximum number of API keys (${s}) for your ${e} plan.`}:{allowed:!0,limit:s}}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>l,w:()=>u});var s=r(55511),a=r.n(s);let i="aes-256-gcm",o=process.env.ROKEY_ENCRYPTION_KEY;if(!o||64!==o.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let n=Buffer.from(o,"hex");function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=a().randomBytes(12),r=a().createCipheriv(i,n,t),s=r.update(e,"utf8","hex");s+=r.final("hex");let o=r.getAuthTag();return`${t.toString("hex")}:${o.toString("hex")}:${s}`}function l(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=Buffer.from(t[0],"hex"),s=Buffer.from(t[1],"hex"),o=t[2];if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==s.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=a().createDecipheriv(i,n,r);u.setAuthTag(s);let l=u.update(o,"hex","utf8");return l+u.final("utf8")}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98279:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>y,serverHooks:()=>A,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{OPTIONS:()=>g,POST:()=>m});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),u=r(39398),l=r(32801);class p{constructor(){this.supabase=(0,u.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY)}async validateApiKey(e,t){try{if(!l.F.isValidFormat(e))return{isValid:!1,error:"Invalid API key format"};let r=l.F.hashApiKey(e),{data:s,error:a}=await this.supabase.from("user_generated_api_keys").select(`
          *,
          custom_api_configs!inner(
            id,
            name,
            user_id,
            routing_strategy,
            routing_strategy_params
          )
        `).eq("key_hash",r).eq("status","active").single();if(a||!s)return{isValid:!1,error:"Invalid or inactive API key"};if(s.expires_at&&new Date(s.expires_at)<new Date)return await this.supabase.from("user_generated_api_keys").update({status:"expired"}).eq("id",s.id),{isValid:!1,error:"API key has expired"};if(s.allowed_ips&&s.allowed_ips.length>0&&t&&!this.checkIpAllowed(t,s.allowed_ips))return{isValid:!1,error:"IP address not allowed for this API key"};return await this.updateLastUsed(s.id,t),{isValid:!0,apiKey:s}}catch(e){return{isValid:!1,error:"Internal server error during validation"}}}checkIpAllowed(e,t){return t.includes(e)||t.includes("*")}async updateLastUsed(e,t){let{data:r}=await this.supabase.from("user_generated_api_keys").select("total_requests").eq("id",e).single(),s={last_used_at:new Date().toISOString(),total_requests:(r?.total_requests||0)+1};t&&(s.last_used_ip=t),await this.supabase.from("user_generated_api_keys").update(s).eq("id",e)}async logApiUsage(e,t,r,s){await this.supabase.from("user_api_key_usage_logs").insert({user_generated_api_key_id:e,user_id:t,custom_api_config_id:r,endpoint:s.endpoint,http_method:s.method,status_code:s.statusCode,ip_address:s.ipAddress,user_agent:s.userAgent,referer:s.referer,model_used:s.modelUsed,provider_used:s.providerUsed,tokens_prompt:s.tokensPrompt,tokens_completion:s.tokensCompletion,cost_usd:s.costUsd,response_time_ms:s.responseTimeMs,error_message:s.errorMessage,error_type:s.errorType})}}class d{constructor(){this.validator=new p}extractApiKey(e){let t=e.headers.get("authorization");if(t){let e=t.match(/^Bearer\s+(.+)$/i);if(e)return e[1]}let r=e.headers.get("x-api-key");return r||null}getClientIp(e){let t=e.headers.get("x-forwarded-for");if(t)return t.split(",")[0].trim();let r=e.headers.get("x-real-ip");if(r)return r;let s=e.headers.get("cf-connecting-ip");return s||"127.0.0.1"}async authenticateRequest(e){try{let t=this.extractApiKey(e);if(!t)return{success:!1,error:'API key is required. Provide it in Authorization header as "Bearer YOUR_API_KEY" or in x-api-key header.',statusCode:401};let r=this.getClientIp(e),s=await this.validator.validateApiKey(t,r);if(!s.isValid){let e=401;return s.error?.includes("expired")?e=401:s.error?.includes("IP address not allowed")&&(e=403),{success:!1,error:s.error||"Invalid API key",statusCode:e}}return{success:!0,userApiKey:s.apiKey,userConfig:s.apiKey.custom_api_configs,ipAddress:r}}catch(e){return{success:!1,error:"Internal authentication error",statusCode:500}}}async logApiUsage(e,t,r,s){try{let a=new URL(t.url);await this.validator.logApiUsage(e.id,e.user_id,e.custom_api_config_id,{endpoint:a.pathname,method:t.method,statusCode:r.statusCode,ipAddress:s,userAgent:t.headers.get("user-agent")||void 0,referer:t.headers.get("referer")||void 0,modelUsed:r.modelUsed,providerUsed:r.providerUsed,tokensPrompt:r.tokensPrompt,tokensCompletion:r.tokensCompletion,costUsd:r.costUsd,responseTimeMs:r.responseTimeMs,errorMessage:r.errorMessage,errorType:r.errorType})}catch(e){}}hasPermission(e,t){let r=e.permissions;switch(t){case"chat":return!0===r.chat;case"streaming":return!0===r.streaming;case"all_models":return!0===r.all_models;default:return!1}}isOriginAllowed(e,t){if(!t)return!0;let r=e.allowed_domains;return!r||0===r.length||r.some(e=>{if("*"===e)return!0;if(e.startsWith("*.")){let r=e.slice(2);return t.endsWith(r)}return t===e||t===`https://${e}`||t===`http://${e}`})}createErrorResponse(e,t){return new Response(JSON.stringify({error:{message:e,type:this.getErrorType(t),code:t}}),{status:t,headers:{"Content-Type":"application/json"}})}getErrorType(e){switch(e){case 401:return"authentication_error";case 403:return"permission_denied";case 500:return"internal_error";default:return"api_error"}}}var c=r(45697);let h=c.z.object({model:c.z.string().optional().default("gpt-3.5-turbo"),messages:c.z.array(c.z.object({role:c.z.enum(["user","assistant","system"]),content:c.z.union([c.z.string(),c.z.array(c.z.any())])})).min(1,{message:"Messages array cannot be empty."}),stream:c.z.boolean().optional().default(!1),temperature:c.z.number().min(0).max(2).optional(),max_tokens:c.z.number().int().positive().optional(),top_p:c.z.number().min(0).max(1).optional(),frequency_penalty:c.z.number().min(-2).max(2).optional(),presence_penalty:c.z.number().min(-2).max(2).optional(),stop:c.z.union([c.z.string(),c.z.array(c.z.string())]).optional(),n:c.z.number().int().positive().optional().default(1),role:c.z.string().optional()}).catchall(c.z.any()),_=new d;async function m(e){try{let t=await _.authenticateRequest(e);if(!t.success)return n.NextResponse.json({error:{message:t.error,type:"authentication_error",code:"invalid_api_key"}},{status:t.statusCode||401});let{userApiKey:r,userConfig:s,ipAddress:a}=t;if(!_.hasPermission(r,"chat"))return n.NextResponse.json({error:{message:"API key does not have chat permission",type:"permission_error",code:"insufficient_permissions"}},{status:403});let i=await e.json(),o=h.safeParse(i);if(!o.success)return n.NextResponse.json({error:{message:"Invalid request body",type:"invalid_request_error",code:"invalid_request",details:o.error.flatten().fieldErrors}},{status:400});let u=o.data;if(u.stream&&!_.hasPermission(r,"streaming"))return n.NextResponse.json({error:{message:"API key does not have streaming permission",type:"permission_error",code:"streaming_not_allowed"}},{status:403});let l={custom_api_config_id:s.id,messages:u.messages,stream:u.stream,temperature:u.temperature,max_tokens:u.max_tokens,role:u.role,model:u.model,top_p:u.top_p,frequency_penalty:u.frequency_penalty,presence_penalty:u.presence_penalty,stop:u.stop,n:u.n,_internal_user_id:s.user_id},p=new URL("/api/v1/chat/completions",e.url),d=await fetch(p.toString(),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Forwarded-For":a||"","X-User-API-Key-ID":r.id,"X-External-Request":"true"},body:JSON.stringify(l)});if(_.logApiUsage(r,e,{statusCode:d.status,modelUsed:u.model},a).catch(e=>{}),u.stream)return new Response(d.body,{status:d.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});{let e=await d.json();if(d.ok)return n.NextResponse.json(e,{status:d.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});return n.NextResponse.json({error:{message:e.error||"Internal server error",type:"server_error",code:"internal_error"}},{status:d.status})}}catch(e){return n.NextResponse.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function g(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/external/v1/chat/completions/route",pathname:"/api/external/v1/chat/completions",filename:"route",bundlePath:"app/api/external/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:A}=y;function v(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,5697],()=>r(98279));module.exports=s})();