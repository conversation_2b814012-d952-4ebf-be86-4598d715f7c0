"use strict";(()=>{var e={};e.id=4454,e.ids=[4454],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},95392:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>_,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{OPTIONS:()=>d,POST:()=>m});var o=r(96559),n=r(48088),a=r(37719),i=r(32190),l=r(49859),u=r(45697);let p=u.z.object({model:u.z.string().optional().default("gpt-3.5-turbo"),messages:u.z.array(u.z.object({role:u.z.enum(["user","assistant","system"]),content:u.z.union([u.z.string(),u.z.array(u.z.any())])})).min(1,{message:"Messages array cannot be empty."}),stream:u.z.boolean().optional().default(!1),temperature:u.z.number().min(0).max(2).optional(),max_tokens:u.z.number().int().positive().optional(),top_p:u.z.number().min(0).max(1).optional(),frequency_penalty:u.z.number().min(-2).max(2).optional(),presence_penalty:u.z.number().min(-2).max(2).optional(),stop:u.z.union([u.z.string(),u.z.array(u.z.string())]).optional(),n:u.z.number().int().positive().optional().default(1),role:u.z.string().optional()}).catchall(u.z.any()),c=new l.S;async function m(e){try{let t,r=await c.authenticateRequest(e);if(!r.success)return i.NextResponse.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:s,userConfig:o,ipAddress:n}=r;if(!c.hasPermission(s,"chat"))return i.NextResponse.json({error:{message:"API key does not have chat permission",type:"permission_error",code:"insufficient_permissions"}},{status:403});let a=await e.json(),l=p.safeParse(a);if(!l.success)return i.NextResponse.json({error:{message:"Invalid request body",type:"invalid_request_error",code:"invalid_request",details:l.error.flatten().fieldErrors}},{status:400});let u=l.data;if(u.stream&&!c.hasPermission(s,"streaming"))return i.NextResponse.json({error:{message:"API key does not have streaming permission",type:"permission_error",code:"streaming_not_allowed"}},{status:403});let m=!1;if(u.role||u.messages?.some(e=>e.content&&"string"==typeof e.content&&e.content.length>100))try{let e=await fetch("https://roukey.online/api/internal/classify-multi-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`},body:JSON.stringify({messages:u.messages,role:u.role,config_id:o.id})});e.ok&&(m=(await e.json()).isMultiRole)}catch(e){}if(m&&!u.stream)return i.NextResponse.json({error:{message:"Multi-role task detected. This request requires async processing to avoid timeouts.",type:"multi_role_detected",code:"require_async_processing",suggestion:{reason:"Gemini classifier detected this task requires multiple specialized roles working together",async_submit_url:"https://roukey.online/api/external/v1/async/submit",estimated_time_minutes:5,benefits:["No timeout limits for complex multi-role orchestration","Progress tracking with role detection","Webhook notifications when complete","Proper handling of role coordination"]}}},{status:409});let d=function(e){let t=0,r=[];(e.messages?.reduce((e,t)=>e+(t.content?.length||0),0)||0)>5e3&&(t+=2,r.push("Long input content")),e.role&&(t+=3,r.push("Role-based routing (multi-role orchestration)")),e.max_tokens&&e.max_tokens>2e3&&(t+=2,r.push("High token output requested")),e.temperature&&e.temperature>1&&(t+=1,r.push("High creativity setting"));let s=["brainstorm","analyze","research","comprehensive","detailed","step by step","multi-step","write a","create a","develop","explain","tutorial","guide","example","code","program","algorithm","implementation","design","build"];e.messages?.some(e=>s.some(t=>e.content?.toLowerCase().includes(t)))&&(t+=3,r.push("Complex task keywords detected")),["brainstorm","write","create","develop","analyze","explain","code"].filter(t=>e.messages?.some(e=>e.content?.toLowerCase().includes(t))).length>=2&&(t+=2,r.push("Multiple tasks in one request"));let o=t>=3,n=Math.min(Math.max(2,Math.ceil(1.5*t)),15);return{recommend:o,reason:o?r.join(", "):void 0,estimatedMinutes:o?n:void 0}}(u);if(d.recommend&&!u.stream&&!m)return i.NextResponse.json({error:{message:"This request is complex and may timeout. Consider using async processing.",type:"complexity_warning",code:"suggest_async_processing",suggestion:{reason:d.reason,async_submit_url:"https://roukey.online/api/external/v1/async/submit",estimated_time_minutes:d.estimatedMinutes,benefits:["No timeout limits","Progress tracking","Webhook notifications","Better handling of complex tasks"]}}},{status:409});let g={custom_api_config_id:o.id,messages:u.messages,stream:u.stream,temperature:u.temperature,max_tokens:u.max_tokens,role:u.role,model:u.model,top_p:u.top_p,frequency_penalty:u.frequency_penalty,presence_penalty:u.presence_penalty,stop:u.stop,n:u.n,_internal_user_id:o.user_id},y=new URL("/api/v1/chat/completions",e.url),h=new AbortController,_=setTimeout(()=>h.abort(),55e3);try{t=await fetch(y.toString(),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Forwarded-For":n||"","X-User-API-Key-ID":s.id,"X-External-Request":"true"},body:JSON.stringify(g),signal:h.signal})}catch(e){if(clearTimeout(_),"AbortError"===e.name)return i.NextResponse.json({error:{message:"Request timeout. For complex multi-role tasks, consider using async processing or breaking down the request.",type:"timeout_error",code:"request_timeout",suggestion:"Try reducing complexity or use streaming for better responsiveness"}},{status:408});throw e}clearTimeout(_);let x=[],f=u.model;if(!u.stream&&t.ok)try{let e=t.clone(),r=await e.json();r.rokey_metadata?.roles_used&&(x=r.rokey_metadata.roles_used),r.model&&(f=r.model)}catch(e){}if(c.logApiUsage(s,e,{statusCode:t.status,modelUsed:f,providerUsed:x.length>0?x.join(", "):void 0},n).catch(e=>{}),u.stream)return new Response(t.body,{status:t.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}});{let e=await t.json();if(!t.ok)return i.NextResponse.json({error:{message:e.error||"Internal server error",type:"server_error",code:"internal_error"}},{status:t.status});{let r={...e,rokey_metadata:{roles_used:e.rokey_metadata?.roles_used||[],routing_strategy:o.routing_strategy,config_name:o.name,api_key_name:s.key_name,...e.rokey_metadata||{}}};return i.NextResponse.json(r,{status:t.status,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Roles-Used":x.join(", ")||"none","X-RouKey-Config":o.name}})}}}catch(e){return i.NextResponse.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function d(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/external/v1/chat/completions/route",pathname:"/api/external/v1/chat/completions",filename:"route",bundlePath:"app/api/external/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:h,serverHooks:_}=g;function x(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:h})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,5697,6485],()=>r(95392));module.exports=s})();