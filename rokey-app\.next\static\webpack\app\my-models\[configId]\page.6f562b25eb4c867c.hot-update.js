"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch API keys for this configuration\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?config_id=\".concat(configId));\n            if (!response.ok) {\n                throw new Error('Failed to fetch API keys');\n            }\n            const data = await response.json();\n            setApiKeys(data.api_keys || []);\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to load API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Fetch subscription info\n    const fetchSubscriptionInfo = async ()=>{\n        try {\n            // Get user profile which includes subscription tier\n            const response = await fetch('/api/user/profile');\n            if (response.ok) {\n                const profile = await response.json();\n                // Get the tier from the user's active subscription\n                const tierResponse = await fetch('/api/user/subscription-tier');\n                const tierData = tierResponse.ok ? await tierResponse.json() : null;\n                const tier = (tierData === null || tierData === void 0 ? void 0 : tierData.tier) || profile.subscription_tier || 'starter';\n                const limits = {\n                    starter: 5,\n                    professional: 25,\n                    enterprise: 100\n                };\n                setSubscriptionInfo({\n                    tier,\n                    keyLimit: limits[tier] || limits.starter,\n                    currentCount: apiKeys.length\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n            // Fallback to starter tier\n            setSubscriptionInfo({\n                tier: 'starter',\n                keyLimit: 2,\n                currentCount: apiKeys.length\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            if (apiKeys.length >= 0) {\n                fetchSubscriptionInfo();\n            }\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        apiKeys\n    ]);\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...keyData,\n                    custom_api_config_id: configId\n                })\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to create API key');\n            }\n            const newApiKey = await response.json();\n            // Show the full API key in a special dialog since it's only shown once\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('API key created successfully!');\n            // Don't refresh the list here - wait until dialog is closed\n            // Don't close the dialog here - let the CreateApiKeyDialog handle it\n            // after the user has seen and copied the full key\n            return newApiKey;\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || 'Failed to create API key');\n            throw error;\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (keyId, updates)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to update API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('API key updated successfully');\n            await fetchApiKeys();\n            setShowEditDialog(false);\n            setSelectedApiKey(null);\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || 'Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (keyId)=>{\n        if (!confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(keyId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.error || 'Failed to revoke API key');\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('API key revoked successfully');\n            await fetchApiKeys();\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || 'Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (keyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === keyId);\n        setSelectedApiKey(apiKey);\n        setShowUsageDialog(true);\n    };\n    const handleCreateDialogClose = (open)=>{\n        setShowCreateDialog(open);\n        // If dialog is being closed, refresh the API keys list\n        if (!open) {\n            fetchApiKeys();\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : true;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"flex items-center justify-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    \"Loading API keys...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"API Keys\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Generate API keys for programmatic access to \",\n                                    configName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        disabled: !canCreateMoreKeys,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                        children: [\n                            \"You are on the \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                variant: \"secondary\",\n                                children: subscriptionInfo.tier\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 28\n                            }, this),\n                            \" plan. You have used \",\n                            subscriptionInfo.currentCount,\n                            \" of \",\n                            subscriptionInfo.keyLimit,\n                            \" API keys.\",\n                            !canCreateMoreKeys && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-600 ml-2\",\n                                children: \"Upgrade your plan to create more API keys.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 239,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            disabled: !canCreateMoreKeys,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_6__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_7__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: handleCreateDialogClose,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_8__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_9__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"FbaUjxeLQ7jhWAVpAmW1g8u12LY=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});