(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7545],{58496:(e,s,t)=>{Promise.resolve().then(t.bind(t,71728))},71728:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(95155),a=t(12115),i=t(35695),l=t(6874),n=t.n(l);t(26784);var o=t(32461);t(15713);var d=t(69598),c=t(14615),m=t(64274),x=t(6865),h=t(64353),u=t(37186),g=t(28960),p=t(15441),b=t(55020),f=t(53951),j=t(14446);let y=[{id:"none",name:"Default Behavior",shortDescription:"Automatic load balancing",description:"<PERSON><PERSON><PERSON><PERSON> will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:u.A},{id:"intelligent_role",name:"Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"Rou<PERSON>ey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:m.A},{id:"complexity_round_robin",name:"Complexity-Based Round-Robin",shortDescription:"Route by prompt complexity",description:"RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:h.A},{id:"strict_fallback",name:"Strict Fallback",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.",icon:p.A},{id:"cost_optimized",name:"Cost-Optimized Routing",shortDescription:"Smart cost-performance balance",description:"RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.",icon:g.A},{id:"ab_routing",name:"A/B Routing",shortDescription:"Continuous model optimization",description:"RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.",icon:c.A}];function N(e){let{apiKey:s,index:t}=e;return(0,r.jsx)(b._EF.q,{value:s,className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300 cursor-grab active:cursor-grabbing",whileDrag:{scale:1.02,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.15)",zIndex:1e3},dragListener:!0,dragControls:void 0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-semibold text-orange-600",children:t+1})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:s.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:[s.provider," - ",s.predefined_model_id]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("div",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors cursor-grab active:cursor-grabbing",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})})]})})}function v(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),t=(0,i.useSearchParams)(),l=e.configId,{getCachedData:d,isCached:v}=(0,f.c)(),w=()=>{let e=t.get("from");if("routing-setup"===e)return"/routing-setup";if("model-config"===e)return"/my-models/".concat(l);{let e=document.referrer,s=window.location.host;if(e&&e.includes(s))try{let s=new URL(e).pathname;if("/routing-setup"===s)return"/routing-setup";if(s==="/my-models/".concat(l)||"/my-models"===s)return"/my-models/".concat(l);s.startsWith("/my-models/")&&s.includes("/routing-setup")}catch(e){}}return"/my-models/".concat(l)},[k,A]=(0,a.useState)(null),[_,S]=(0,a.useState)(!0),[C,R]=(0,a.useState)(null),[L,B]=(0,a.useState)(null),[P,F]=(0,a.useState)(!1),[q,I]=(0,a.useState)("none"),[K,E]=(0,a.useState)({}),[z,M]=(0,a.useState)([]),[D,H]=(0,a.useState)(!1),[T,W]=(0,a.useState)([]),[O,G]=(0,a.useState)(null),[U,J]=(0,a.useState)({}),[Q,Y]=(0,a.useState)([]),[V,X]=(0,a.useState)(!1),[Z,$]=(0,a.useState)(!1),[ee,es]=(0,a.useState)(null),[et,er]=(0,a.useState)(null),ea=(0,a.useCallback)(async()=>{if(!l){R("Configuration ID is missing."),S(!1);return}let e=d(l);if(e&&e.configDetails&&e.apiKeys){var s;A(e.configDetails),M(e.apiKeys);let t=e.routingStrategy||"none";if(I(t),E(e.routingParams||{}),"strict_fallback"===t&&(null==(s=e.routingParams)?void 0:s.ordered_api_key_ids)){let s=e.routingParams.ordered_api_key_ids;W([...s.map(s=>e.apiKeys.find(e=>e.id===s)).filter(Boolean),...e.apiKeys.filter(e=>!s.includes(e.id))])}else W([...e.apiKeys]);S(!1),H(!1);return}v(l)||F(!0),S(!0),H(!0),R(null),B(null);try{let e=await fetch("/api/custom-configs/".concat(l));if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configuration")}let s=await e.json();A(s);let t=s.routing_strategy||"none";I(t);let r=s.routing_strategy_params||{};E(r);let a=await fetch("/api/keys?custom_config_id=".concat(l));if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to fetch API keys for this configuration")}let i=await a.json();if(M(i),"strict_fallback"===t&&r.ordered_api_key_ids){let e=r.ordered_api_key_ids,s=e.map(e=>i.find(s=>s.id===e)).filter(Boolean),t=i.filter(s=>!e.includes(s.id));W([...s,...t])}else W([...i])}catch(e){R("Error loading data: ".concat(e.message)),A(null),M([])}finally{S(!1),H(!1),F(!1)}},[l,d,v]);(0,a.useEffect)(()=>{ea()},[ea]);let ei=(0,a.useCallback)(async e=>{if(l&&e){X(!0),es(null),er(null);try{let s=await fetch("/api/custom-configs/".concat(l,"/keys/").concat(e,"/complexity-assignments"));if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to fetch complexity assignments")}let t=await s.json();J(s=>({...s,[e]:t})),Y(t)}catch(e){es("Error fetching assignments for key: ".concat(e.message)),Y([])}finally{X(!1)}}},[l]);(0,a.useEffect)(()=>{O?ei(O):(Y([]),es(null))},[O,ei]);let el=(e,s)=>{Y(t=>s?[...t,e].sort((e,s)=>e-s):t.filter(s=>s!==e))},en=(0,a.useCallback)(async()=>{if(!l||!O)return void es("No API key selected to save assignments for.");$(!0),es(null),er(null);try{let e=await fetch("/api/custom-configs/".concat(l,"/keys/").concat(O,"/complexity-assignments"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({complexity_levels:Q})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save complexity assignments")}let s=await e.json();J(e=>({...e,[O]:[...Q]})),er(s.message||"Complexity assignments saved successfully!")}catch(e){es("Error saving assignments: ".concat(e.message))}finally{$(!1)}},[l,O,Q]),eo=e=>{W(e),E({ordered_api_key_ids:e.map(e=>e.id)})},ed=async e=>{if(e.preventDefault(),!l||!k)return void R("Configuration details not loaded.");S(!0),R(null),B(null);let s=K;"strict_fallback"===q&&(s={ordered_api_key_ids:T.map(e=>e.id)});try{let e=await fetch("/api/custom-configs/".concat(l,"/routing"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({routing_strategy:q,routing_strategy_params:s})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save routing settings")}let t=await e.json();B(t.message||"Routing settings saved successfully!"),A(e=>e?{...e,routing_strategy:q,routing_strategy_params:s}:null),E(s)}catch(e){R("Error saving settings: ".concat(e.message))}finally{S(!1)}},ec=()=>{var e;return"complexity_round_robin"!==q?null:(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Complexity-Based Key Assignments"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity."}),D&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading API keys..."})]}),!D&&0===z.length&&(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsx)("p",{className:"text-sm text-yellow-800",children:"No API keys found for this configuration. Please add API keys first on the model configuration page."})}),z.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"apiKeyForComplexity",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Key to Assign Complexities:"}),(0,r.jsxs)("select",{id:"apiKeyForComplexity",value:O||"",onChange:e=>G(e.target.value||null),className:"form-select max-w-md",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"-- Select an API Key --"}),z.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.label," (",e.provider," - ",e.predefined_model_id,")"]},e.id))]})]}),O&&(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-gray-900 mb-4",children:["Assign Complexity Levels for: ",(0,r.jsx)("span",{className:"text-orange-600",children:null==(e=z.find(e=>e.id===O))?void 0:e.label})]}),V&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 ml-2",children:"Loading current assignments..."})]}),ee&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:ee})}),et&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:et})}),!V&&(0,r.jsx)("div",{className:"space-y-3 mb-6",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200",children:[(0,r.jsx)("input",{type:"checkbox",checked:Q.includes(e),onChange:s=>el(e,s.target.checked),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["Complexity Level ",e]})]},e))}),(0,r.jsx)("button",{onClick:en,disabled:Z||V,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:Z?"Saving Assignments...":"Save Assignments for this Key"})]})]})};return P&&!v(l)?(0,r.jsx)(j.Ay,{}):_&&!k?(0,r.jsx)(j.CE,{}):!C||k||_?(0,r.jsx)("div",{className:"min-h-screen bg-cream",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("button",{onClick:()=>{if(window.history.length>1){w();let e=document.referrer,t=window.location.host;if(e&&e.includes(t))return void s.back()}s.push(w())},className:"btn-secondary inline-flex items-center text-sm",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-2"}),(()=>{let e=t.get("from");if("routing-setup"===e)return"Back to Routing Setup";if("model-config"===e)return"Back to Configuration";{let e=document.referrer,s=window.location.host;if(e&&e.includes(s))try{let s=new URL(e).pathname;if("/routing-setup"===s)return"Back to Routing Setup";s==="/my-models/".concat(l)||"/my-models"===s||s.startsWith("/my-models/")}catch(e){}}return"Back to Configuration"})()]})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h1",{className:"text-h1 text-gray-900",children:"Advanced Routing Setup"}),k&&(0,r.jsxs)("p",{className:"text-body-sm text-gray-600 mt-1",children:["Configuration: ",(0,r.jsx)("span",{className:"text-orange-600 font-semibold",children:k.name})]})]})]})}),C&&!L&&(0,r.jsx)("div",{className:"card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-red-800",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-700 mt-1",children:C})]})]})}),L&&(0,r.jsx)("div",{className:"card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"Settings Saved"}),(0,r.jsx)("p",{className:"text-green-700 mt-1",children:L})]})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6 sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Routing Strategy"}),(0,r.jsx)("div",{className:"space-y-3",children:y.map(e=>{let s=e.icon,t=q===e.id;return(0,r.jsx)("button",{onClick:()=>{if(I(e.id),"strict_fallback"===e.id){let e=K.ordered_api_key_ids;e&&Array.isArray(e)?W([...e.map(e=>z.find(s=>s.id===e)).filter(Boolean),...z.filter(s=>!e.includes(s.id))]):W([...z]),E({ordered_api_key_ids:T.map(e=>e.id)})}else E({}),W([...z]);G(null),Y([]),es(null),er(null)},disabled:_,className:"w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ".concat(t?"border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]":"border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"p-3 rounded-lg transition-colors duration-300 ".concat(t?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600"),children:(0,r.jsx)(s,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm transition-colors duration-300 ".concat(t?"text-orange-900":"text-gray-900"),children:e.name}),t&&(0,r.jsx)(x.A,{className:"w-4 h-4 text-orange-600 animate-in fade-in duration-300"})]}),(0,r.jsx)("p",{className:"text-xs leading-relaxed transition-colors duration-300 ".concat(t?"text-orange-700":"text-gray-600"),children:e.shortDescription})]})]})},e.id)})})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("form",{onSubmit:ed,children:(0,r.jsx)("div",{className:"card p-8 min-h-[600px]",children:(0,r.jsx)("div",{className:"animate-in fade-in slide-in-from-right-4 duration-500",children:(()=>{let e=y.find(e=>e.id===q);return"none"===q?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(u.A,{className:"w-10 h-10 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Default Behavior"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-md mx-auto leading-relaxed",children:null==e?void 0:e.description}),(0,r.jsx)("div",{className:"mt-8 p-4 bg-green-50 border border-green-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-green-800 font-medium",children:"No additional setup required"})]})}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"intelligent_role"===q?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Intelligent Role Routing"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,r.jsx)("p",{children:"System analyzes your prompt to understand the main task"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,r.jsx)("p",{children:"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,r.jsx)("p",{children:"Routes to assigned API key or falls back to 'Default General Chat Model'"})]})]})]}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. Future enhancements may allow further customization."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"strict_fallback"===q?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(p.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Strict Fallback Configuration"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),D&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin"}),(0,r.jsx)("p",{className:"text-gray-600 ml-3",children:"Loading API keys..."})]}),!D&&0===z.length&&(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-900 mb-2",children:"No API Keys Found"}),(0,r.jsx)("p",{className:"text-yellow-800 leading-relaxed",children:"Please add API keys on the main configuration page to set up fallback order."})]}),!D&&z.length>0&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("p",{className:"text-sm text-blue-800 leading-relaxed",children:"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on."})]})}),(0,r.jsx)(b._EF.Y,{axis:"y",values:T,onReorder:eo,className:"space-y-3",children:T.map((e,s)=>(0,r.jsx)(N,{apiKey:e,index:s},e.id))})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_||0===z.length,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"complexity_round_robin"===q?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(h.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Complexity-Based Round-Robin"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),ec(),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"cost_optimized"===q?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(g.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"Smart Cost-Optimized Routing"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"RouKey Classifier"})," analyzes your prompt to determine task complexity (Simple, Moderate, Complex)"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,r.jsxs)("p",{children:["Routes to appropriate cost tier: ",(0,r.jsx)("strong",{children:"Simple tasks"})," → Cheapest models, ",(0,r.jsx)("strong",{children:"Complex tasks"})," → Premium models"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,r.jsx)("p",{children:"Prioritizes cost savings by using cheaper models whenever possible while ensuring quality for complex tasks"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-green-900 text-sm",children:"Simple Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-green-800 leading-relaxed",children:["Basic questions, simple conversations, straightforward requests → ",(0,r.jsx)("strong",{children:"Cheapest models"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-yellow-900 text-sm",children:"Moderate Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-yellow-800 leading-relaxed",children:["Analysis, explanations, moderate complexity → ",(0,r.jsx)("strong",{children:"Balanced pricing models"})]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-purple-900 text-sm",children:"Complex Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-purple-800 leading-relaxed",children:["Advanced reasoning, coding, research → ",(0,r.jsx)("strong",{children:"Premium models"})]})]})]}),(0,r.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(g.A,{className:"w-6 h-6 text-orange-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"Intelligent Cost Optimization"}),(0,r.jsx)("p",{className:"text-sm text-orange-800 leading-relaxed",children:"This strategy maximizes cost savings by routing most requests to cheaper models, while automatically upgrading to premium models only when task complexity truly requires it. Perfect for balancing budget constraints with quality requirements."})]})]})}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. RouKey will automatically classify task complexity and route to the most cost-effective model tier for optimal savings and performance."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"ab_routing"===q?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-gray-900 mb-4 flex items-center",children:[(0,r.jsx)(c.A,{className:"w-7 h-7 mr-3 text-orange-600"}),"A/B Routing Optimization"]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:null==e?void 0:e.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-800",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"1"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"85% Control Group:"})," Routes to your best-performing models based on historical data"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"2"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"15% Test Group:"})," Experiments with different models to discover better options"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-600",children:"3"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Continuous Learning:"})," Automatically updates routing based on quality metrics and user feedback"]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-green-900 text-sm",children:"Quality Optimization"})]}),(0,r.jsx)("p",{className:"text-xs text-green-800 leading-relaxed",children:"Tracks response quality, user satisfaction, and task completion rates to identify the best models for your specific use cases."})]}),(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-xl p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-purple-900 text-sm",children:"Cost Efficiency"})]}),(0,r.jsx)("p",{className:"text-xs text-purple-800 leading-relaxed",children:"Balances quality with cost to find models that deliver the best value for your specific requirements and budget."})]})]}),(0,r.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(c.A,{className:"w-6 h-6 text-orange-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-orange-900 mb-2",children:"Intelligent Experimentation"}),(0,r.jsx)("p",{className:"text-sm text-orange-800 leading-relaxed",children:"This strategy continuously learns from your usage patterns to optimize routing decisions. The more you use it, the better it becomes at selecting the perfect model for each request."})]})]})}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-900 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-800 leading-relaxed",children:"No additional setup required. RouKey will automatically start A/B testing and learning from your requests to optimize routing performance."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-200 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):null})()})})})})]})})]})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Routing Setup Error"}),(0,r.jsxs)("div",{className:"card border-red-200 bg-red-50 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,r.jsx)("p",{className:"text-red-800",children:C})]}),(0,r.jsx)(n(),{href:"/my-models",className:"mt-4 btn-primary inline-block",children:"Back to My Models"})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5738,9968,6060,4609,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(58496)),_N_E=e.O()}]);