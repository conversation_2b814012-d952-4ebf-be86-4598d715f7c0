"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7706],{3269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ACTION_HEADER:function(){return r},FLIGHT_HEADERS:function(){return c},NEXT_DID_POSTPONE_HEADER:function(){return h},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return u},NEXT_HMR_REFRESH_HEADER:function(){return i},NEXT_IS_PRERENDER_HEADER:function(){return y},NEXT_REWRITTEN_PATH_HEADER:function(){return _},NEXT_REWRITTEN_QUERY_HEADER:function(){return E},NEXT_ROUTER_PREFETCH_HEADER:function(){return a},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return l},NEXT_ROUTER_STALE_TIME_HEADER:function(){return p},NEXT_ROUTER_STATE_TREE_HEADER:function(){return o},NEXT_RSC_UNION_QUERY:function(){return f},NEXT_URL:function(){return s},RSC_CONTENT_TYPE_HEADER:function(){return d},RSC_HEADER:function(){return n}});let n="RSC",r="Next-Action",o="Next-Router-State-Tree",a="Next-Router-Prefetch",l="Next-Router-Segment-Prefetch",i="Next-HMR-Refresh",u="__next_hmr_refresh_hash__",s="Next-Url",d="text/x-component",c=[n,o,a,i,l],f="_rsc",p="x-nextjs-stale-time",h="x-nextjs-postponed",_="x-nextjs-rewritten-path",E="x-nextjs-rewritten-query",y="x-nextjs-prerender";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5449:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),n(13668);let r=n(20589);{let e=n.u;n.u=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return(0,r.encodeURIPath)(e(...n))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6002:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,n(66905).patchConsoleError)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6634:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return _},dispatchNavigateAction:function(){return g},dispatchTraverseAction:function(){return b},getCurrentAppRouterState:function(){return E},publicAppRouterInstance:function(){return v}});let r=n(69818),o=n(29726),a=n(12115),l=n(95122);n(86005);let i=n(81027),u=n(85929),s=n(56158),d=n(89154),c=n(24930);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,o=t.state;t.pending=n;let a=n.payload,i=t.action(o,a);function u(e){n.discarded||(t.state=e,f(t,r),n.resolve(e))}(0,l.isThenable)(i)?i.then(u,e=>{f(t,r),n.reject(e)}):u(i)}let h=null;function _(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let o={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let l={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=l,p({actionQueue:e,action:l,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,l.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:l,setState:n})):(null!==e.last&&(e.last.next=l),e.last=l)})(n,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};if(null!==h)throw Object.defineProperty(Error("Internal Next.js Error: createMutableActionQueue was called more than once"),"__NEXT_ERROR_CODE",{value:"E624",enumerable:!1,configurable:!0});return h=n,n}function E(){return null!==h?h.state:null}function y(){return null!==h?h.onRouterTransitionStart:null}function g(e,t,n,o){let a=new URL((0,u.addBasePath)(e),location.href);(0,c.setLinkForCurrentNavigation)(o);let l=y();null!==l&&l(e,t),(0,i.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,s.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function b(e,t){let n=y();null!==n&&n(e,"traverse"),(0,i.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let v={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){if(null===h)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});return h}(),o=(0,s.createPrefetchURL)(e);if(null!==o){var a;(0,d.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:o,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;g(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;g(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};window.next&&(window.next.router=v),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13950:(e,t)=>{function n(e,t){let n=e[e.length-1];n&&n.stack===t.stack||e.push(t)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"enqueueConsecutiveDedupedError",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21315:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let r=n(85929);function o(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22858:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let r=n(36494),o=n(62210);function a(e){return(0,o.isRedirectError)(e)||(0,r.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24930:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return s},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return g},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return v},onNavigationIntent:function(){return R},pingVisibleLinks:function(){return T},setLinkForCurrentNavigation:function(){return d},unmountLinkForCurrentNavigation:function(){return c},unmountPrefetchableInstance:function(){return b}}),n(6634);let r=n(56158),o=n(69818),a=n(86005),l=n(12115),i=null,u={pending:!0},s={pending:!1};function d(e){(0,l.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(s),null==e||e.setOptimisticLinkStatus(u),i=e})}function c(e){i===e&&(i=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;v(t.target,e)}},{rootMargin:"200px"}):null;function _(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==h&&h.observe(e)}function E(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,o,a){if(o){let o=E(t);if(null!==o){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:a};return _(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function g(e,t,n,r){let o=E(t);null!==o&&_(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==h&&h.unobserve(e)}function v(e,t){let n=f.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),m(n))}function R(e,t){let n=f.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,m(n))}function m(e){var t;let n=e.prefetchTask;if(!e.isVisible){null!==n&&(0,a.cancelPrefetchTask)(n);return}t=e,(async()=>t.router.prefetch(t.prefetchHref,{kind:t.kind}))().catch(e=>{})}function T(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of p){let l=r.prefetchTask;if(null!==l&&r.cacheVersion===n&&l.key.nextUrl===e&&l.treeAtTimeOfPrefetch===t)continue;null!==l&&(0,a.cancelPrefetchTask)(l);let i=(0,a.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(i,t,r.kind===o.PrefetchKind.FULL,u),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26043:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createConsoleError:function(){return o},getConsoleErrorType:function(){return l},isConsoleError:function(){return a}});let n=Symbol.for("next.console.error.digest"),r=Symbol.for("next.console.error.type");function o(e,t){let o="string"==typeof e?Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}):e;return o[n]="NEXT_CONSOLE_ERROR",o[r]="string"==typeof e?"string":"error",t&&!o.environmentName&&(o.environmentName=t),o}let a=e=>e&&"NEXT_CONSOLE_ERROR"===e[n],l=e=>e[r];("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26465:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NEXTJS_HYDRATION_ERROR_LINK:function(){return u},REACT_HYDRATION_ERROR_LINK:function(){return i},getDefaultHydrationErrorMessage:function(){return s},getHydrationErrorStackInfo:function(){return h},isHydrationError:function(){return d},isReactHydrationErrorMessage:function(){return c},testReactHydrationWarning:function(){return p}});let r=n(38466)._(n(15807)),o=/hydration failed|while hydrating|content does not match|did not match|HTML didn't match|text didn't match/i,a="Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:",l=[a,"Hydration failed because the server rendered text didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:","A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:"],i="https://react.dev/link/hydration-mismatch",u="https://nextjs.org/docs/messages/react-hydration-error",s=()=>a;function d(e){return(0,r.default)(e)&&o.test(e.message)}function c(e){return l.some(t=>e.startsWith(t))}let f=[/^In HTML, (.+?) cannot be a child of <(.+?)>\.(.*)\nThis will cause a hydration error\.(.*)/,/^In HTML, (.+?) cannot be a descendant of <(.+?)>\.\nThis will cause a hydration error\.(.*)/,/^In HTML, text nodes cannot be a child of <(.+?)>\.\nThis will cause a hydration error\./,/^In HTML, whitespace text nodes cannot be a child of <(.+?)>\. Make sure you don't have any extra whitespace between tags on each line of your source code\.\nThis will cause a hydration error\./,/^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\.(.*)/,/^Expected server HTML to contain a matching text node for "(.+?)" in <(.+?)>\.(.*)/,/^Did not expect server HTML to contain the text node "(.+?)" in <(.+?)>\.(.*)/,/^Text content did not match\. Server: "(.+?)" Client: "(.+?)"(.*)/];function p(e){return"string"==typeof e&&!!e&&(e.startsWith("Warning: ")&&(e=e.slice(9)),f.some(t=>t.test(e)))}function h(e){let t=p(e=(e=e.replace(/^Error: /,"")).replace("Warning: ",""));if(!c(e)&&!t)return{message:null,stack:e,diff:""};if(t){let[t,n]=e.split("\n\n");return{message:t.trim(),stack:"",diff:(n||"").trim()}}let n=e.indexOf("\n"),[r,o]=(e=e.slice(n+1).trim()).split(""+i),a=r.trim();if(!o||!(o.length>1))return{message:a,stack:o};{let e=[],t=[];return o.split("\n").forEach(n=>{""!==n.trim()&&(n.trim().startsWith("at ")?e.push(n):t.push(n))}),{message:a,diff:t.join("\n"),stack:e.join("\n")}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26614:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ErrorBoundary:function(){return h},ErrorBoundaryHandler:function(){return c},GlobalError:function(){return f},default:function(){return p}});let r=n(38466),o=n(95155),a=r._(n(12115)),l=n(19921),i=n(22858);n(38836);let u=void 0,s={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function d(e){let{error:t}=e;if(u){let e=u.getStore();if((null==e?void 0:e.isRevalidate)||(null==e?void 0:e.isStaticGeneration))throw t}return null}class c extends a.default.Component{static getDerivedStateFromError(e){if((0,i.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){let{error:n}=t;return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(d,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,o.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,n=null==t?void 0:t.digest;return(0,o.jsxs)("html",{id:"__next_error__",children:[(0,o.jsx)("head",{}),(0,o.jsxs)("body",{children:[(0,o.jsx)(d,{error:t}),(0,o.jsx)("div",{style:s.error,children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{style:s.text,children:["Application error: a ",n?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",n?"server logs":"browser console"," for more information)."]}),n?(0,o.jsx)("p",{style:s.text,children:"Digest: "+n}):null]})})]})]})}let p=f;function h(e){let{errorComponent:t,errorStyles:n,errorScripts:r,children:a}=e,i=(0,l.useUntrackedPathname)();return t?(0,o.jsx)(c,{pathname:i,errorComponent:t,errorStyles:n,errorScripts:r,children:a}):(0,o.jsx)(o.Fragment,{children:a})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35415:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),n(5449);let r=n(36188),o=n(51408);(0,r.appBootstrap)(()=>{let{hydrate:e}=n(64486);n(56158),n(87555),e(o)}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36494:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTTPAccessErrorStatus:function(){return n},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return i},getAccessFallbackHTTPStatus:function(){return l},isHTTPAccessFallbackError:function(){return a}});let n={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},r=new Set(Object.values(n)),o="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,n]=e.digest.split(";");return t===o&&r.has(Number(n))}function l(e){return Number(e.digest.split(";")[1])}function i(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43954:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),(0,n(65444).handleGlobalErrors)(),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46975:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTTPAccessFallbackBoundary",{enumerable:!0,get:function(){return d}});let r=n(93011),o=n(95155),a=r._(n(12115)),l=n(19921),i=n(36494);n(43230);let u=n(95227);class s extends a.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,i.isHTTPAccessFallbackError)(e))return{triggeredStatus:(0,i.getAccessFallbackHTTPStatus)(e)};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.triggeredStatus?{triggeredStatus:void 0,previousPathname:e.pathname}:{triggeredStatus:t.triggeredStatus,previousPathname:e.pathname}}render(){let{notFound:e,forbidden:t,unauthorized:n,children:r}=this.props,{triggeredStatus:a}=this.state,l={[i.HTTPAccessErrorStatus.NOT_FOUND]:e,[i.HTTPAccessErrorStatus.FORBIDDEN]:t,[i.HTTPAccessErrorStatus.UNAUTHORIZED]:n};if(a){let u=a===i.HTTPAccessErrorStatus.NOT_FOUND&&e,s=a===i.HTTPAccessErrorStatus.FORBIDDEN&&t,d=a===i.HTTPAccessErrorStatus.UNAUTHORIZED&&n;return u||s||d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{name:"robots",content:"noindex"}),!1,l[a]]}):r}return r}constructor(e){super(e),this.state={triggeredStatus:void 0,previousPathname:e.pathname}}}function d(e){let{notFound:t,forbidden:n,unauthorized:r,children:i}=e,d=(0,l.useUntrackedPathname)(),c=(0,a.useContext)(u.MissingSlotContext);return t||n||r?(0,o.jsx)(s,{pathname:d,notFound:t,forbidden:n,unauthorized:r,missingSlots:c,children:i}):(0,o.jsx)(o.Fragment,{children:i})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53506:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"attachHydrationErrorState",{enumerable:!0,get:function(){return a}});let r=n(26465),o=n(89771);function a(e){let t={},n=(0,r.testReactHydrationWarning)(e.message),a=(0,r.isHydrationError)(e);if(!(a||n))return;let l=(0,o.getReactHydrationDiffSegments)(e.message);if(l){let i=l[1];t={...e.details,...o.hydrationErrorState,warning:(i&&!n?null:o.hydrationErrorState.warning)||[(0,r.getDefaultHydrationErrorMessage)(),"",""],notes:n?"":l[0],reactOutputComponentDiff:i},!o.hydrationErrorState.reactOutputComponentDiff&&i&&(o.hydrationErrorState.reactOutputComponentDiff=i),!i&&a&&o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff)}else o.hydrationErrorState.warning&&(t={...e.details,...o.hydrationErrorState}),o.hydrationErrorState.reactOutputComponentDiff&&(t.reactOutputComponentDiff=o.hydrationErrorState.reactOutputComponentDiff);e.details=t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56158:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return P},default:function(){return M},isExternalURL:function(){return x}});let r=n(93011),o=n(95155),a=r._(n(12115)),l=n(95227),i=n(69818),u=n(11139),s=n(886),d=n(81027),c=r._(n(26614)),f=n(10774),p=n(85929),h=n(67760),_=n(20686),E=n(72691),y=n(71822),g=n(44882),b=n(87102),v=n(68946),R=n(38836),m=n(6634),T=n(36825),O=n(62210);n(24930);let j={};function x(e){return e.origin!==window.location.origin}function P(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return x(t)?null:t}function w(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(o,"",r)):window.history.replaceState(o,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function N(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function C(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,o=null!==r?r:n;return(0,a.useDeferredValue)(n,o)}function A(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,f=(0,d.useActionQueue)(n),{canonicalUrl:p}=f,{searchParams:R,pathname:x}=(0,a.useMemo)(()=>{let e=new URL(p,window.location.href);return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,g.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(j.pendingMpaPath=void 0,(0,d.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,O.isRedirectError)(t)){e.preventDefault();let n=(0,T.getURLFromRedirectError)(t);(0,T.getRedirectTypeFromError)(t)===O.RedirectType.push?m.publicAppRouterInstance.push(n,{}):m.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:P}=f;if(P.mpaNavigation){if(j.pendingMpaPath!==p){let e=window.location;P.pendingPush?e.assign(p):e.replace(p),j.pendingMpaPath=p}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,d.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=N(t),o&&n(o)),e(t,r,o)},window.history.replaceState=function(e,r,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=N(e),o&&n(o)),t(e,r,o)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,m.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:S,tree:A,nextUrl:M,focusAndScrollRef:H}=f,L=(0,a.useMemo)(()=>(0,E.findHeadInCache)(S,A[1]),[S,A]),I=(0,a.useMemo)(()=>(0,v.getSelectedParams)(A),[A]),k=(0,a.useMemo)(()=>({parentTree:A,parentCacheNode:S,parentSegmentPath:null,url:p}),[A,S,p]),U=(0,a.useMemo)(()=>({tree:A,focusAndScrollRef:H,nextUrl:M}),[A,H,M]);if(null!==L){let[e,n]=L;t=(0,o.jsx)(C,{headCacheNode:e},n)}else t=null;let F=(0,o.jsxs)(_.RedirectBoundary,{children:[t,S.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:A})]});return F=(0,o.jsx)(c.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:F}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(w,{appRouterState:f}),(0,o.jsx)(D,{}),(0,o.jsx)(s.PathParamsContext.Provider,{value:I,children:(0,o.jsx)(s.PathnameContext.Provider,{value:x,children:(0,o.jsx)(s.SearchParamsContext.Provider,{value:R,children:(0,o.jsx)(l.GlobalLayoutRouterContext.Provider,{value:U,children:(0,o.jsx)(l.AppRouterContext.Provider,{value:m.publicAppRouterInstance,children:(0,o.jsx)(l.LayoutRouterContext.Provider,{value:k,children:F})})})})})})]})}function M(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,R.useNavFailureHandler)(),(0,o.jsx)(c.ErrorBoundary,{errorComponent:c.default,children:(0,o.jsx)(A,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let H=new Set,L=new Set;function D(){let[,e]=a.default.useState(0),t=H.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return L.add(n),t!==H.size&&n(),()=>{L.delete(n)}},[t,e]),[...H].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=H.size;return H.add(e),H.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63678:(e,t,n)=>{function r(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return r}}),n(36494).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65444:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleClientError:function(){return b},handleConsoleError:function(){return g},handleGlobalErrors:function(){return T},useErrorHandler:function(){return v}});let r=n(38466),o=n(12115),a=n(53506),l=n(22858),i=n(89771),u=n(85169),s=r._(n(15807)),d=n(26043),c=n(13950),f=n(95128),p=globalThis.queueMicrotask||(e=>Promise.resolve().then(e)),h=[],_=[],E=[],y=[];function g(e,t){let n,{environmentName:r}=(0,u.parseConsoleArgs)(t);for(let o of(n=(0,s.default)(e)?(0,d.createConsoleError)(e,r):(0,d.createConsoleError)((0,u.formatConsoleArgs)(t),r),n=(0,f.getReactStitchedError)(n),(0,i.storeHydrationErrorStateFromConsoleArgs)(...t),(0,a.attachHydrationErrorState)(n),(0,c.enqueueConsecutiveDedupedError)(h,n),_))p(()=>{o(n)})}function b(e){let t;for(let n of(t=(0,s.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t=(0,f.getReactStitchedError)(t),(0,a.attachHydrationErrorState)(t),(0,c.enqueueConsecutiveDedupedError)(h,t),_))p(()=>{n(t)})}function v(e,t){(0,o.useEffect)(()=>(h.forEach(e),E.forEach(t),_.push(e),y.push(t),()=>{_.splice(_.indexOf(e),1),y.splice(y.indexOf(t),1),h.splice(0,h.length),E.splice(0,E.length)}),[e,t])}function R(e){if((0,l.isNextRouterError)(e.error))return e.preventDefault(),!1;e.error&&b(e.error)}function m(e){let t=null==e?void 0:e.reason;if((0,l.isNextRouterError)(t))return void e.preventDefault();let n=t;for(let e of(n&&!(0,s.default)(n)&&(n=Object.defineProperty(Error(n+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})),E.push(n),y))e(n)}function T(){try{Error.stackTraceLimit=50}catch(e){}window.addEventListener("error",R),window.addEventListener("unhandledrejection",m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66905:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{originConsoleError:function(){return o},patchConsoleError:function(){return a}}),n(38466),n(15807);let r=n(22858);n(65444),n(85169);let o=globalThis.console.error;function a(){window.console.error=function(){let e;for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];e=n[0],(0,r.isNextRouterError)(e)||o.apply(window.console,n)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67760:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return l}});let r=n(12115),o=n(47650),a="next-route-announcer";function l(e){let{tree:t}=e,[n,l]=(0,r.useState)(null);(0,r.useEffect)(()=>(l(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,r.useState)(""),s=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==s.current&&s.current!==e&&u(e),s.current=e},[t]),n?(0,o.createPortal)(i,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87555:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return P}});let r=n(38466),o=n(93011),a=n(95155),l=n(69818),i=o._(n(12115)),u=r._(n(47650)),s=n(95227),d=n(88586),c=n(71822),f=n(26614),p=n(31127),h=n(24189),_=n(20686),E=n(46975),y=n(85637),g=n(4108),b=n(81027),v=u.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=["bottom","height","left","right","top","width","x","y"];function m(e,t){let n=e.getBoundingClientRect();return n.top>=0&&n.top<=t}class T extends i.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,n)=>(0,p.matchSegment)(t,e[n]))))return;let n=null,r=e.hashFragment;if(r&&(n=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(r)),n||(n=(0,v.findDOMNode)(this)),!(n instanceof Element))return;for(;!(n instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return R.every(e=>0===t[e])}(n);){if(null===n.nextElementSibling)return;n=n.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,h.handleSmoothScroll)(()=>{if(r)return void n.scrollIntoView();let e=document.documentElement,t=e.clientHeight;!m(n,t)&&(e.scrollTop=0,m(n,t)||n.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,n.focus()}}}}function O(e){let{segmentPath:t,children:n}=e,r=(0,i.useContext)(s.GlobalLayoutRouterContext);if(!r)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});return(0,a.jsx)(T,{segmentPath:t,focusAndScrollRef:r.focusAndScrollRef,children:n})}function j(e){let{tree:t,segmentPath:n,cacheNode:r,url:o}=e,u=(0,i.useContext)(s.GlobalLayoutRouterContext);if(!u)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});let{tree:f}=u,h=null!==r.prefetchRsc?r.prefetchRsc:r.rsc,_=(0,i.useDeferredValue)(r.rsc,h),E="object"==typeof _&&null!==_&&"function"==typeof _.then?(0,i.use)(_):_;if(!E){let e=r.lazyData;if(null===e){let t=function e(t,n){if(t){let[r,o]=t,a=2===t.length;if((0,p.matchSegment)(n[0],r)&&n[1].hasOwnProperty(o)){if(a){let t=e(void 0,n[1][o]);return[n[0],{...n[1],[o]:[t[0],t[1],t[2],"refetch"]}]}return[n[0],{...n[1],[o]:e(t.slice(2),n[1][o])}]}}return n}(["",...n],f),a=(0,g.hasInterceptionRouteInCurrentTree)(f),s=Date.now();r.lazyData=e=(0,d.fetchServerResponse)(new URL(o,location.origin),{flightRouterState:t,nextUrl:a?u.nextUrl:null}).then(e=>((0,i.startTransition)(()=>{(0,b.dispatchAppRouterAction)({type:l.ACTION_SERVER_PATCH,previousTree:f,serverResponse:e,navigatedAt:s})}),e)),(0,i.use)(e)}(0,i.use)(c.unresolvedThenable)}return(0,a.jsx)(s.LayoutRouterContext.Provider,{value:{parentTree:t,parentCacheNode:r,parentSegmentPath:n,url:o},children:E})}function x(e){let t,{loading:n,children:r}=e;if(t="object"==typeof n&&null!==n&&"function"==typeof n.then?(0,i.use)(n):n){let e=t[0],n=t[1],o=t[2];return(0,a.jsx)(i.Suspense,{fallback:(0,a.jsxs)(a.Fragment,{children:[n,o,e]}),children:r})}return(0,a.jsx)(a.Fragment,{children:r})}function P(e){let{parallelRouterKey:t,error:n,errorStyles:r,errorScripts:o,templateStyles:l,templateScripts:u,template:d,notFound:c,forbidden:p,unauthorized:h}=e,g=(0,i.useContext)(s.LayoutRouterContext);if(!g)throw Object.defineProperty(Error("invariant expected layout router to be mounted"),"__NEXT_ERROR_CODE",{value:"E56",enumerable:!1,configurable:!0});let{parentTree:b,parentCacheNode:v,parentSegmentPath:R,url:m}=g,T=v.parallelRoutes,P=T.get(t);P||(P=new Map,T.set(t,P));let w=b[0],S=b[1][t],N=S[0],C=null===R?[t]:R.concat([w,t]),A=(0,y.createRouterCacheKey)(N),M=(0,y.createRouterCacheKey)(N,!0),H=P.get(A);if(void 0===H){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};H=e,P.set(A,e)}let L=v.loading;return(0,a.jsxs)(s.TemplateContext.Provider,{value:(0,a.jsx)(O,{segmentPath:C,children:(0,a.jsx)(f.ErrorBoundary,{errorComponent:n,errorStyles:r,errorScripts:o,children:(0,a.jsx)(x,{loading:L,children:(0,a.jsx)(E.HTTPAccessFallbackBoundary,{notFound:c,forbidden:p,unauthorized:h,children:(0,a.jsx)(_.RedirectBoundary,{children:(0,a.jsx)(j,{url:m,tree:S,cacheNode:H,segmentPath:C})})})})})}),children:[l,u,d]},M)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89771:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getHydrationWarningType:function(){return i},getReactHydrationDiffSegments:function(){return d},hydrationErrorState:function(){return o},storeHydrationErrorStateFromConsoleArgs:function(){return c}});let r=n(26465),o={},a=new Set(["Warning: In HTML, %s cannot be a child of <%s>.%s\nThis will cause a hydration error.%s","Warning: In HTML, %s cannot be a descendant of <%s>.\nThis will cause a hydration error.%s","Warning: In HTML, text nodes cannot be a child of <%s>.\nThis will cause a hydration error.","Warning: In HTML, whitespace text nodes cannot be a child of <%s>. Make sure you don't have any extra whitespace between tags on each line of your source code.\nThis will cause a hydration error.","Warning: Expected server HTML to contain a matching <%s> in <%s>.%s","Warning: Did not expect server HTML to contain a <%s> in <%s>.%s"]),l=new Set(['Warning: Expected server HTML to contain a matching text node for "%s" in <%s>.%s','Warning: Did not expect server HTML to contain the text node "%s" in <%s>.%s']),i=e=>{if("string"!=typeof e)return"text";let t=e.startsWith("Warning: ")?e:"Warning: "+e;return u(t)?"tag":s(t)?"text-in-tag":"text"},u=e=>a.has(e),s=e=>l.has(e),d=e=>{if(e){let{message:t,diff:n}=(0,r.getHydrationErrorStackInfo)(e);if(t)return[t,n]}};function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[a,l,u,...s]=t;if((0,r.testReactHydrationWarning)(a)){let e=a.startsWith("Warning: ");3===t.length&&(u="");let n=[a,l,u],r=(s[s.length-1]||"").trim();e?o.reactOutputComponentDiff=function(e,t,n,r){let o=-1,a=-1,l=i(e),u=r.split("\n").map((e,r)=>{e=e.trim();let[,l,i]=/at (\w+)( \((.*)\))?/.exec(e)||[];return i||(l===t&&-1===o?o=r:l===n&&-1===a&&(a=r)),i?"":l}).filter(Boolean).reverse(),s="";for(let e=0;e<u.length;e++){let t=u[e],n="tag"===l&&e===u.length-o-1,r="tag"===l&&e===u.length-a-1;n||r?s+="> "+" ".repeat(Math.max(2*e-2,0)+2)+"<"+t+">\n":s+=" ".repeat(2*e+2)+"<"+t+">\n"}if("text"===l){let e=" ".repeat(2*u.length);s+="+ "+e+'"'+t+'"\n'+("- "+e+'"'+n)+'"\n'}else if("text-in-tag"===l){let e=" ".repeat(2*u.length);s+="> "+e+"<"+n+">\n"+(">   "+e+'"'+t)+'"\n'}return s}(a,l,u,r):o.reactOutputComponentDiff=r,o.warning=n,o.serverContent=l,o.clientContent=u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90894:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let r=n(95155);function o(e){let{Component:t,searchParams:o,params:a,promises:l}=e;{let{createRenderSearchParamsFromClient:e}=n(67205),l=e(o),{createRenderParamsFromClient:i}=n(33558),u=i(a);return(0,r.jsx)(t,{params:u,searchParams:l})}}n(39837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94970:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientSegmentRoot",{enumerable:!0,get:function(){return o}});let r=n(95155);function o(e){let{Component:t,slots:o,params:a,promise:l}=e;{let{createRenderParamsFromClient:e}=n(33558),l=e(a);return(0,r.jsx)(t,{...o,params:l})}}n(39837),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95128:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return s}});let r=n(38466),o=r._(n(12115)),a=r._(n(15807)),l=n(29148),i="react-stack-bottom-frame",u=RegExp("(at "+i+" )|("+i+"\\@)");function s(e){let t=(0,a.default)(e),n=t&&e.stack||"",r=t?e.message:"",i=n.split("\n"),s=i.findIndex(e=>u.test(e)),d=s>=0?i.slice(0,s).join("\n"):n,c=Object.defineProperty(Error(r),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(c,e),(0,l.copyNextErrorCode)(e,c),c.stack=d,function(e){if(!o.default.captureOwnerStack)return;let t=e.stack||"",n=o.default.captureOwnerStack();n&&!1===t.endsWith(n)&&(e.stack=t+=n)}(c),c}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);