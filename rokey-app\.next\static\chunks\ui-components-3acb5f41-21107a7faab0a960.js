"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6060],{8413:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(12115),i=a(57514);let l=new Map;function n(e){let{configId:s,onRetry:a,className:n="",disabled:d=!1}=e,[c,o]=(0,r.useState)(!1),[m,u]=(0,r.useState)([]),[g,x]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),b=(0,r.useRef)(null),v=(0,r.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=l.get(s);if(e&&Date.now()-e.timestamp<3e5){u(e.keys),p(!0);return}}x(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);l.set(s,{keys:a,timestamp:Date.now()}),u(a),p(!0)}}catch(e){}finally{x(!1)}}},[s]);(0,r.useEffect)(()=>{s&&!h&&v(!0)},[s,v,h]),(0,r.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&o(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[c]);let y=e=>{o(!1),a(e)};return(0,t.jsxs)("div",{className:"relative ".concat(n),ref:b,children:[(0,t.jsxs)("button",{onClick:()=>{c||0!==m.length||h||v(!0),o(!c)},disabled:d,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(d?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,t.jsx)(i.E,{className:"w-4 h-4 stroke-2 ".concat(g?"animate-spin":"")}),(0,t.jsx)(i.D,{className:"w-3 h-3 stroke-2"})]}),c&&(0,t.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),v(!1)},disabled:g,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,t.jsx)(i.E,{className:"w-3 h-3 ".concat(g?"animate-spin":"")})})]}),(0,t.jsxs)("button",{onClick:()=>y(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)(i.E,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||g)&&(0,t.jsx)("div",{className:"border-t border-gray-100 my-1"}),g&&(0,t.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,t.jsxs)("button",{onClick:()=>y(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:g,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!g&&0===m.length&&h&&(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!g&&(0,t.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,t.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=l.get(s);return e&&Date.now()-e.timestamp<3e5?(0,t.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>r,CE:()=>i});var t=a(95155);function r(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function i(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},38050:(e,s,a)=>{a.d(s,{default:()=>n});var t=a(12115),r=a(35695),i=a(5777),l=a(44042);function n(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:n=!0}=e,d=(0,r.usePathname)(),c=(0,t.useRef)(""),o=(0,t.useRef)(0),{exportMetrics:m}=(0,l.D)("PerformanceTracker");return(0,t.useEffect)(()=>{if(!a)return;let e=c.current;e&&e!==d&&(i.zf.trackNavigation(e,d),performance.now(),o.current),c.current=d,o.current=performance.now()},[d,a]),(0,t.useEffect)(()=>{if(!n)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&i.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?i.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?i.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&i.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[n]),(0,t.useEffect)(()=>{let e;if(!s)return;let a=!1,t=0,r=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;t=Math.max(t,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),t>80&&("/"===d?(i.zf.schedulePrefetch("/pricing"),i.zf.schedulePrefetch("/features")):"/features"===d&&i.zf.schedulePrefetch("/auth/signup")),t=0},150)},l=performance.now(),n=()=>{performance.now()-l>1e4&&("/"===d?i.zf.schedulePrefetch("/auth/signup"):"/pricing"===d&&i.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",r,{passive:!0});let c=()=>{document.hidden&&n()};document.addEventListener("visibilitychange",c);let o=()=>{n()};return window.addEventListener("beforeunload",o),()=>{clearTimeout(e),window.removeEventListener("scroll",r),document.removeEventListener("visibilitychange",c),window.removeEventListener("beforeunload",o),n()}},[d,s,m]),(0,t.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,t=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),t.disconnect()}}},[]),null}},69903:(e,s,a)=>{a.d(s,{A:()=>g});var t=a(95155),r=a(12115),i=a(35695),l=a(99323);let n=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,t.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"py-20",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,t.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),o=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),u=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function g(e){let s,{targetRoute:a,children:g}=e,[x,h]=(0,r.useState)(!0),[p,b]=(0,r.useState)(!1),v=(0,i.usePathname)(),y=(0,r.useRef)(),{isPageCached:j}=(0,l.bu)()||{isPageCached:()=>!1};return((0,r.useEffect)(()=>(v===a&&(y.current=setTimeout(()=>{b(!0),setTimeout(()=>h(!1),100)},j(a)?50:200)),()=>{y.current&&clearTimeout(y.current)}),[v,a,j]),(0,r.useEffect)(()=>{h(!0),b(!1)},[a]),v!==a&&x||v===a&&x&&!p)?(0,t.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,t.jsx)(n,{}):s.startsWith("/pricing")?(0,t.jsx)(d,{}):s.startsWith("/features")?(0,t.jsx)(c,{}):s.startsWith("/auth/")?(0,t.jsx)(o,{}):s.startsWith("/playground")?(0,t.jsx)(m,{}):(0,t.jsx)(u,{})}):(0,t.jsx)("div",{className:"transition-opacity duration-300 ".concat(p?"opacity-100":"opacity-0"),children:g})}},78817:(e,s,a)=>{a.d(s,{A:()=>d});var t=a(95155);a(12115);var r=a(89732),i=a(99323),l=a(95565);let n={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:r.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:r.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:r.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:r.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:r.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:r.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:r.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function d(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,i.bu)()||{clearNavigation:()=>{}};if(!(s?n[s]:null))return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,t.jsx)(r.cu,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let d=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,t.jsx)(r.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,t.jsx)(l.O2,{});case"/my-models":return(0,t.jsx)(l.MyModelsSkeleton,{});case"/playground":return(0,t.jsx)(d,{});case"/routing-setup":return(0,t.jsx)(l.RoutingSetupSkeleton,{});case"/logs":return(0,t.jsx)(c,{});case"/training":return(0,t.jsx)(l.vD,{});case"/analytics":return(0,t.jsx)(l.AnalyticsSkeleton,{})}})()]})}},95060:(e,s,a)=>{a.d(s,{A:()=>b});var t=a(95155),r=a(6874),i=a.n(r),l=a(66766),n=a(35695),d=a(12115),c=a(8652),o=a(14097),m=a(22261),u=a(99323),g=a(37843),x=a(24403),h=a(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:c.fA,iconSolid:o.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:c.RY,iconSolid:o.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:c.cu,iconSolid:o.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:c.sR,iconSolid:o.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:c.AQ,iconSolid:o.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:c.tl,iconSolid:o.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:c.r9,iconSolid:o.r9,description:"Advanced insights"}];function b(){let e=(0,n.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:r,setHovered:c}=(0,m.c)(),{navigateOptimistically:o}=(0,u.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:b}=(0,g.C)(),{prefetchWhenIdle:v}=(0,g.e)(),{prefetchChatHistory:y}=(0,x.l2)(),{predictions:j,isLearning:f}=(0,h.x)(),N=(0,h.G)();(0,d.useEffect)(()=>{let s=p.map(e=>e.href),a=j.slice(0,2),t=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return v([...a,...t,...s.filter(s=>s!==e&&!a.includes(s)&&!t.includes(s)),"/playground","/logs"].slice(0,6))},[e,v,j,N,f]);let w=!s||a;return(0,t.jsx)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!r&&c(!0),onMouseLeave:()=>!r&&c(!1),children:(0,t.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,t.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,t.jsx)(l.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,t.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,t.jsx)("nav",{className:"space-y-2",children:p.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),r=a?s.iconSolid:s.icon,l=j.includes(s.href),n=N.find(e=>e.route===s.href),d="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){b(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&y(e)}}}:b(s.href,50);return(0,t.jsx)(i(),{href:s.href,onClick:e=>{e.preventDefault(),o(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...d,children:(0,t.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,t.jsx)(r,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),l&&!a&&(0,t.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,t.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:s.label}),n&&!a&&(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===n.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===n.priority?"!":"\xb7"})]}),(0,t.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:n?n.reason:s.description})]})]})},s.href)})})]})})})}},95494:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(6874),i=a.n(r),l=a(12115),n=a(41045),d=a(22261),c=a(35695),o=a(83298),m=a(52643);function u(){var e,s,a,r,u,g,x;let{isCollapsed:h,isHovered:p,toggleSidebar:b}=(0,d.c)(),v=(0,c.usePathname)(),{user:y,subscriptionStatus:j}=(0,o.R)(),[f,N]=(0,l.useState)(!1),[w,k]=(0,l.useState)(!1),E=(0,m.u)();(0,l.useEffect)(()=>{let e=()=>{k(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let C=(null==y||null==(e=y.user_metadata)?void 0:e.first_name)||(null==y||null==(a=y.user_metadata)||null==(s=a.full_name)?void 0:s.split(" ")[0])||"User",P=C.charAt(0).toUpperCase()+((null==y||null==(g=y.user_metadata)||null==(u=g.last_name)||null==(r=u.charAt(0))?void 0:r.toUpperCase())||(null==(x=C.charAt(1))?void 0:x.toUpperCase())||"U"),L=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(v),S=(null==j?void 0:j.hasActiveSubscription)?(null==j?void 0:j.tier)==="starter"?"Starter Plan":(null==j?void 0:j.tier)==="professional"?"Professional Plan":(null==j?void 0:j.tier)==="enterprise"?"Enterprise Plan":"Starter Plan":"Starter Plan",A=async()=>{try{await E.auth.signOut(),window.location.href="/auth/signin"}catch(e){}};return(0,t.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:(0,t.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(w&&(!h||p)?"max-w-7xl mx-auto":w?"max-w-none":"max-w-7xl mx-auto"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:b,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,t.jsx)(n.tK,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)("span",{children:L.title}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:L.subtitle})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsx)("div",{className:"hidden xl:block",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,t.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,t.jsx)(n.XF,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,t.jsxs)("div",{className:"hidden sm:block relative",children:[(0,t.jsxs)("button",{onClick:()=>N(!f),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)(n.Vy,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)(n.D3,{className:"h-3 w-3 text-gray-600 transition-transform duration-200 ".concat(f?"rotate-180":"")})]}),f&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>N(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,t.jsxs)(i(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,t.jsx)(n.Vy,{className:"h-4 w-4 mr-3 text-gray-500"}),"Settings"]}),(0,t.jsxs)(i(),{href:"/dashboard/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,t.jsx)(n.rM,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing"]}),(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("button",{onClick:A,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,t.jsx)(n.Rz,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:P})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:C}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:S})]})]})]})]})})})}}}]);