(()=>{var e={};e.id=8700,e.ids=[1489,8700],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>i});var s=r(34386),a=r(44999);async function i(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32801:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(55511),a=r.n(s),i=r(56534);class n{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static generateApiKey(){let e=a().randomBytes(this.RANDOM_PART_LENGTH/2).toString("hex"),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,s=`${r}_${t}`,i=this.hashApiKey(s);return{fullKey:s,prefix:r,secretPart:t,hash:i}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let s=0;s<e;s++){let e=a().randomInt(0,t.length);r+=t[e]}return r}static hashApiKey(e){return a().createHash("sha256").update(e).digest("hex")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static encryptSuffix(e){let t=e.slice(-4);return(0,i.w)(t)}static decryptSuffix(e){try{return(0,i.Y)(e)}catch(e){return"xxxx"}}static createMaskedKey(e,t){let r=this.decryptSuffix(t),s=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(s)}${r}`}static validateSubscriptionLimits(e,t){let r={starter:5,professional:25,enterprise:100},s=r[e]||r.starter;return t>=s?{allowed:!1,limit:s,message:`You have reached the maximum number of API keys (${s}) for your ${e} plan.`}:{allowed:!0,limit:s}}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>p,w:()=>u});var s=r(55511),a=r.n(s);let i="aes-256-gcm",n=process.env.ROKEY_ENCRYPTION_KEY;if(!n||64!==n.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let o=Buffer.from(n,"hex");function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=a().randomBytes(12),r=a().createCipheriv(i,o,t),s=r.update(e,"utf8","hex");s+=r.final("hex");let n=r.getAuthTag();return`${t.toString("hex")}:${n.toString("hex")}:${s}`}function p(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=Buffer.from(t[0],"hex"),s=Buffer.from(t[1],"hex"),n=t[2];if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==s.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=a().createDecipheriv(i,o,r);u.setAuthTag(s);let p=u.update(n,"hex","utf8");return p+u.final("utf8")}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73877:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>y,GET:()=>l,PATCH:()=>_});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(2507),p=r(32801),d=r(45697);let c=d.z.object({key_name:d.z.string().min(1).max(100).optional(),status:d.z.enum(["active","inactive"]).optional(),expires_at:d.z.string().datetime().nullable().optional()});async function l(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a,error:i}=await r.from("user_generated_api_keys").select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        last_used_ip,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name
        )
      `).eq("id",s).eq("user_id",e.id).single();if(i||!a)return o.NextResponse.json({error:"API key not found"},{status:404});let n={...a,masked_key:p.F.createMaskedKey(a.key_prefix,a.encrypted_key_suffix),encrypted_key_suffix:void 0};return o.NextResponse.json({api_key:n})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function _(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let i=await e.json(),n=c.parse(i),{data:u,error:d}=await r.from("user_generated_api_keys").select("id, user_id, custom_api_config_id").eq("id",s).eq("user_id",t.id).single();if(d||!u)return o.NextResponse.json({error:"API key not found or access denied"},{status:404});if(n.key_name){let{data:e}=await r.from("user_generated_api_keys").select("id").eq("custom_api_config_id",u.custom_api_config_id).eq("key_name",n.key_name).eq("status","active").neq("id",s).single();if(e)return o.NextResponse.json({error:"An API key with this name already exists for this configuration"},{status:409})}let{data:l,error:_}=await r.from("user_generated_api_keys").update({...n,updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",t.id).select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at
      `).single();if(_)return o.NextResponse.json({error:"Failed to update API key"},{status:500});let y={...l,masked_key:p.F.createMaskedKey(l.key_prefix,l.encrypted_key_suffix),encrypted_key_suffix:void 0};return o.NextResponse.json({api_key:y})}catch(e){if(e instanceof d.z.ZodError)return o.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a,error:i}=await r.from("user_generated_api_keys").select("id, user_id, key_name").eq("id",s).eq("user_id",e.id).single();if(i||!a)return o.NextResponse.json({error:"API key not found or access denied"},{status:404});let{error:n}=await r.from("user_generated_api_keys").update({status:"revoked",updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",e.id);if(n)return o.NextResponse.json({error:"Failed to revoke API key"},{status:500});return o.NextResponse.json({message:"API key revoked successfully"},{status:200})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user-api-keys/[keyId]/route",pathname:"/api/user-api-keys/[keyId]",filename:"route",bundlePath:"app/api/user-api-keys/[keyId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user-api-keys\\[keyId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=f;function g(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410,5697],()=>r(73877));module.exports=s})();