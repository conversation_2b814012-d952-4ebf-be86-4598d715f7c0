(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9899],{21104:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>b});var a=i(95155),s=i(12115),r=i(55020),o=i(56075),l=i(75961),n=i(89416),c=i(14615),d=i(64274),m=i(29337),x=i(64353),p=i(37186),u=i(28960),h=i(15441),f=i(86474),y=i(50956);let g=[{id:"none",name:"Default Load Balancing",shortDescription:"Automatic load balancing",description:"RouKey automatically load balances across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:p.A,features:["Automatic load distribution","Built-in retry mechanisms","Zero configuration required","High availability"],useCase:"Perfect for simple setups where you want reliable distribution across multiple API keys without complex routing logic.",performance:"Excellent reliability with automatic failover",complexity:"Beginner",color:"from-gray-500 to-gray-600"},{id:"intelligent_role",name:"RouKey's Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RouKey uses advanced AI to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:d.A,features:["RouKey's AI-powered classification","Dynamic role assignment","Context-aware routing","Fallback to default model"],useCase:"Ideal for applications with diverse use cases like coding, writing, analysis, and general chat.",performance:"Superior accuracy with RouKey's proprietary classification",complexity:"Intermediate",color:"from-[#ff6b35] to-[#f7931e]",featured:!0},{id:"complexity_round_robin",name:"RouKey's Complexity-Based Routing",shortDescription:"Route by prompt complexity",description:"RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:x.A,features:["Intelligent complexity analysis","Optimized model selection","Cost-performance balance","Proximal level fallback"],useCase:"Perfect for cost optimization - route simple tasks to cheaper models and complex tasks to premium models.",performance:"Optimal cost-performance ratio",complexity:"Advanced",color:"from-blue-500 to-blue-600"},{id:"strict_fallback",name:"Strict Fallback Strategy",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.",icon:h.A,features:["Predictable routing order","Guaranteed fallback chain","Manual priority control","Reliable failover"],useCase:"Best for scenarios where you have a preferred model hierarchy and want guaranteed fallback behavior.",performance:"Highly predictable with manual control",complexity:"Intermediate",color:"from-green-500 to-green-600"},{id:"cost_optimized",name:"RouKey's Smart Cost Optimization",shortDescription:"Intelligent cost-performance balance",description:"RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.",icon:u.A,features:["RouKey's learning algorithms","Dynamic cost optimization","Quality preservation","Automatic model selection"],useCase:"Essential for production applications where cost control is critical but quality cannot be compromised.",performance:"Maximum cost savings with quality assurance",complexity:"Advanced",color:"from-emerald-500 to-emerald-600",featured:!0},{id:"ab_routing",name:"RouKey's A/B Testing Router",shortDescription:"Continuous model optimization",description:"RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.",icon:c.A,features:["Continuous optimization","Data-driven decisions","Performance tracking","Automatic improvements"],useCase:"Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.",performance:"Self-improving performance over time",complexity:"Advanced",color:"from-purple-500 to-purple-600"}];function b(){let[e,t]=(0,s.useState)(g[1]);return(0,a.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200 pt-20",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)(r.PY1.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-4xl md:text-5xl font-bold text-black mb-6",children:["RouKey's Advanced ",(0,a.jsx)("span",{className:"text-[#ff6b35]",children:"Routing Strategies"})]}),(0,a.jsx)(r.PY1.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-xl text-gray-600 max-w-3xl mx-auto mb-8",children:"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration."}),(0,a.jsx)(r.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:(0,a.jsxs)(y.A,{href:"/pricing",className:"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors",children:["View Pricing Plans",(0,a.jsx)(n.A,{className:"ml-2 h-5 w-5"})]})})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:g.map((i,s)=>(0,a.jsxs)(r.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},onClick:()=>t(i),className:"relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ".concat(e.id===i.id?"border-[#ff6b35] bg-[#ff6b35]/5":"border-gray-200 hover:border-gray-300"),children:[i.featured&&(0,a.jsx)("div",{className:"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold",children:"Featured"}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(i.color),children:(0,a.jsx)(i.icon,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-black mb-2",children:i.name}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:i.shortDescription}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Beginner"===i.complexity?"bg-green-100 text-green-800":"Intermediate"===i.complexity?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:i.complexity}),e.id===i.id&&(0,a.jsx)(m.A,{className:"h-5 w-5 text-[#ff6b35]"})]})]})]})]},i.id))})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)("div",{className:"sticky top-8",children:(0,a.jsxs)(r.PY1.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"bg-white border border-gray-200 rounded-xl p-6 shadow-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(e.color),children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-black",children:e.name}),(0,a.jsx)("p",{className:"text-gray-600",children:e.shortDescription})]})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-6",children:e.description}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Key Features"}),(0,a.jsx)("ul",{className:"space-y-2",children:e.features.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-[#ff6b35]"}),(0,a.jsx)("span",{className:"text-gray-700 text-sm",children:e})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Best Use Case"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:e.useCase})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-black mb-2",children:"Performance"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm",children:e.performance})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,a.jsxs)(y.A,{href:"/pricing",className:"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors",children:[(0,a.jsx)(f.A,{className:"mr-2 h-5 w-5"}),"Get Started"]})})]},e.id)})})]})}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Ready to Optimize Your AI Routing?"}),(0,a.jsx)("p",{className:"text-xl text-white/90 mb-8",children:"Start with any strategy and switch anytime. No configuration required."}),(0,a.jsxs)(y.A,{href:"/auth/signup?plan=professional",className:"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg",children:["Start Building Now",(0,a.jsx)(n.A,{className:"ml-3 h-5 w-5"})]})]})}),(0,a.jsx)(l.A,{})]})}},44383:(e,t,i)=>{"use strict";i.d(t,{f:()=>s.A,t:()=>a.A});var a=i(69598),s=i(74500)},46235:(e,t,i)=>{Promise.resolve().then(i.bind(i,21104))}},e=>{var t=t=>e(e.s=t);e.O(0,[7871,2115,5738,9968,6060,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(46235)),_N_E=e.O()}]);