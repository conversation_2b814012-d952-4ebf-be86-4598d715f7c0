"use strict";(()=>{var e={};e.id=157,e.ids=[157],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},25517:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>i});var o=t(96559),a=t(48088),n=t(37719),u=t(32190),p=t(49859);async function i(e){try{Object.fromEntries(e.headers.entries());let r=e.headers.get("authorization"),t=e.headers.get("x-api-key"),s=new p.S,o=await s.authenticateRequest(e);return u.NextResponse.json({debug:{headers:{authorization:r,"x-api-key":t},environment:{supabaseUrl:!0,serviceRoleKey:!!process.env.SUPABASE_SERVICE_ROLE_KEY},authResult:o}})}catch(e){return u.NextResponse.json({error:"Debug test failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/debug/auth-test/route",pathname:"/api/debug/auth-test",filename:"route",bundlePath:"app/api/debug/auth-test/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\debug\\auth-test\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:c,serverHooks:l}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:c})}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,6485],()=>t(25517));module.exports=s})();