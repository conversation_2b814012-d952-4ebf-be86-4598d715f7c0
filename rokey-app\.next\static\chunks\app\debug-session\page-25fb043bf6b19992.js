(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2303],{92017:(e,s,t)=>{Promise.resolve().then(t.bind(t,92405))},92405:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var n=t(95155),a=t(12115),i=t(52643);function r(){let[e,s]=(0,a.useState)(null),[t,r]=(0,a.useState)(!0),l=(0,i.u)();(0,a.useEffect)(()=>{let e=async()=>{try{let{data:{user:e},error:t}=await l.auth.getUser();s({hasSession:!!e,sessionError:null==t?void 0:t.message,userId:null==e?void 0:e.id,userEmail:null==e?void 0:e.email,userMetadata:null==e?void 0:e.user_metadata,accessToken:"N/A (using getUser)",refreshToken:"N/A (using getUser)",expiresAt:"N/A (using getUser)",timestamp:new Date().toISOString()})}catch(e){s({error:e instanceof Error?e.message:String(e),timestamp:new Date().toISOString()})}finally{r(!1)}};e();let{data:{subscription:t}}=l.auth.onAuthStateChange((s,t)=>{e()});return()=>t.unsubscribe()},[]);let o=async()=>{try{r(!0);let{data:e,error:s}=await l.auth.signInWithPassword({email:"<EMAIL>",password:"test123456"});s?alert("Sign in error: "+s.message):alert("Sign in successful!")}catch(e){alert("Sign in error: "+(e instanceof Error?e.message:String(e)))}finally{r(!1)}},d=async()=>{try{r(!0),await l.auth.signOut(),alert("Signed out successfully!")}catch(e){alert("Sign out error: "+(e instanceof Error?e.message:String(e)))}finally{r(!1)}};return t?(0,n.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,n.jsx)("p",{children:"Loading session info..."})]})}):(0,n.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Session Debug Page"}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Current Session Info"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Actions"}),(0,n.jsxs)("div",{className:"space-x-4",children:[(0,n.jsx)("button",{onClick:o,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",disabled:t,children:"Test Sign In"}),(0,n.jsx)("button",{onClick:d,className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600",disabled:t,children:"Test Sign Out"}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600",children:"Refresh Page"})]})]}),(0,n.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"Instructions:"}),(0,n.jsxs)("ol",{className:"list-decimal list-inside text-yellow-700 space-y-1",children:[(0,n.jsx)("li",{children:"Check the current session info above"}),(0,n.jsx)("li",{children:'Try the "Test Sign In" button (update the email/password in the code if needed)'}),(0,n.jsx)("li",{children:"Check if session info updates after sign in"}),(0,n.jsx)("li",{children:"Try navigating to /checkout after successful sign in"})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(92017)),_N_E=e.O()}]);