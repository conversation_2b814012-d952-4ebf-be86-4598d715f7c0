(()=>{var e={};e.id=8403,e.ids=[8403],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},42247:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{POST:()=>u});var i=r(96559),o=r(48088),n=r(37719),a=r(32190);let l=(0,r(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function u(e){try{let t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return a.NextResponse.json({error:"Unauthorized"},{status:401});if(t.substring(7)!==process.env.ROKEY_API_ACCESS_TOKEN)return a.NextResponse.json({error:"Invalid token"},{status:401});let{messages:r,role:s,config_id:i}=await e.json();if(!r||!i)return a.NextResponse.json({error:"messages and config_id are required"},{status:400});let o=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!o)return a.NextResponse.json({isMultiRole:!1,reason:"No classification API key available"});let{data:n,error:u}=await l.from("roles").select("*").eq("custom_api_config_id",i).eq("status","active");if(u||!n||0===n.length)return a.NextResponse.json({isMultiRole:!1,reason:"No active roles found for this config"});let p=r.filter(e=>"user"===e.role).map(e=>e.content).join(" ").substring(0,2e3);if(!p.trim())return a.NextResponse.json({isMultiRole:!1,reason:"No user content found in messages"});let d=await c(p,n,o);return a.NextResponse.json({isMultiRole:d.isMultiRole,roles:d.roles,reasoning:d.reasoning,rolesCount:d.roles.length})}catch(e){return a.NextResponse.json({isMultiRole:!1,reason:"Classification error"},{status:500})}}async function c(e,t,r){let s=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),i=`You are RouKey's Multi-Role Task Analyzer. Your job is to determine if a user request requires multiple distinct specialized roles working together, or if it can be handled by a single role.

IMPORTANT GUIDELINES:
1. Only classify as multi-role if the task GENUINELY requires multiple distinct specialized skills
2. Simple requests that can be handled by one role should be classified as single-role
3. Consider the complexity and scope of the request
4. Look for tasks that involve multiple distinct phases or require different types of expertise

Examples of MULTI-ROLE tasks:
- "Research quantum computing, then write code, then create a business plan" (research + coding + business)
- "Analyze this data, create visualizations, and write a marketing strategy" (analysis + design + marketing)
- "Debug this code, optimize performance, and write documentation" (debugging + optimization + writing)

Examples of SINGLE-ROLE tasks:
- "Write a Python script" (coding only)
- "Explain quantum physics" (explanation only)
- "Create a business plan" (business only)
- "Brainstorm ideas" (creative thinking only)

Respond with a JSON object containing:
{
  "isMultiRole": boolean,
  "roles": [{"roleId": "role_id", "confidence": 0.8, "executionOrder": 1}],
  "reasoning": "explanation of why this is/isn't multi-role"
}`,o=`Available Roles:
${s}

User Request: "${e.substring(0,2e3)}"

Analyze this request: Does it require multiple distinct specialized roles working together, or can it be handled by a single role?`;try{let e,s=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Multi-Role-Detection)"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:i},{role:"user",content:o}],temperature:.3,max_tokens:800,response_format:{type:"json_object"}})});if(!s.ok)return{isMultiRole:!1,roles:[],reasoning:"API error during multi-role detection"};let n=await s.json();try{return e=JSON.parse(n.choices[0].message.content),"boolean"!=typeof e.isMultiRole&&(e.isMultiRole=!1),Array.isArray(e.roles)||(e.roles=[]),e.isMultiRole&&e.roles.length>0&&(e.roles=e.roles.map(e=>{let r=t.find(t=>t.id===e.roleId||t.id.toLowerCase()===e.roleId.toLowerCase()||t.name&&t.name.toLowerCase()===e.roleId.toLowerCase());return r?{...e,roleId:r.id,confidence:"number"==typeof e.confidence?e.confidence:.8}:null}).filter(Boolean),0===e.roles.length&&(e.isMultiRole=!1,e.reasoning="No valid roles matched after filtering")),e}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error parsing multi-role detection result"}}}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error during multi-role detection"}}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/internal/classify-multi-role/route",pathname:"/api/internal/classify-multi-role",filename:"route",bundlePath:"app/api/internal/classify-multi-role/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\internal\\classify-multi-role\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:m}=p;function f(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398],()=>r(42247));module.exports=s})();