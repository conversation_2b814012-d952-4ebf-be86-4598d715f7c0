(()=>{var e={};e.id=7637,e.ids=[7637],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10258:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413);function a(){return(0,r.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-2/3"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 space-y-6",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/3"})]})]})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38149:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["training",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79729)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,10258)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/training/page",pathname:"/training",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52587:(e,t,s)=>{Promise.resolve().then(s.bind(s,69407))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...u},p)=>(0,r.createElement)("svg",{ref:p,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:l("lucide",i),...!n&&!o(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...i},o)=>(0,r.createElement)(c,{ref:o,iconNode:t,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69407:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(43210),i=s(62688);let n=(0,i.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),l=(0,i.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);var o=s(93613);let d=(0,i.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),c=(0,i.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),u=(0,i.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var p=s(11860);function m({configId:e,onDocumentUploaded:t}){let[s,i]=(0,a.useState)([]),[m,x]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[f,y]=(0,a.useState)(0),[v,j]=(0,a.useState)(!1),[b,w]=(0,a.useState)(null),[N,k]=(0,a.useState)(null),A=(0,a.useRef)(null),_=(0,a.useCallback)(async(t=0,s)=>{if(e){0===t&&g(!0);try{let r=await fetch(`/api/documents/list?configId=${e}`);if(r.ok){let e=(await r.json()).documents||[];if(s&&t<3&&!e.find(e=>e.id===s))return void setTimeout(()=>{_(t+1,s)},(t+1)*500);i(t=>{let s=new Set(t.map(e=>e.id)),r=e.filter(e=>!s.has(e.id));return[...t.map(t=>e.find(e=>e.id===t.id)||t),...r].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())})}}catch(e){t<2&&setTimeout(()=>{_(t+1,s)},1e3)}finally{(0===t||s)&&g(!1)}}},[e]),C=async s=>{if(!e)return void w("Please select an API configuration first");let r=s[0];if(r){if(!["application/pdf","text/plain","text/markdown"].includes(r.type))return void w("Please upload PDF, TXT, or MD files only");if(r.size>0xa00000)return void w("File size must be less than 10MB");x(!0),w(null),k(null),y(0);try{let s=new FormData;s.append("file",r),s.append("configId",e);let a=setInterval(()=>{y(e=>Math.min(e+10,90))},200),n=await fetch("/api/documents/upload",{method:"POST",body:s});if(clearInterval(a),y(100),!n.ok){let e=await n.json();throw Error(e.error||"Upload failed")}let l=await n.json();k(`✨ ${r.name} uploaded successfully! Processing ${l.document.chunks_total} chunks.`);let o={id:l.document.id,filename:l.document.filename||r.name,file_type:r.type,file_size:r.size,status:l.document.status||"processing",chunks_count:l.document.chunks_processed||0,created_at:new Date().toISOString()};i(e=>e.find(e=>e.id===o.id)?e.map(e=>e.id===o.id?o:e):[o,...e]),setTimeout(async()=>{await _(0,l.document.id)},200),t?.()}catch(e){w(`Upload failed: ${e.message}`),setTimeout(()=>w(null),8e3)}finally{x(!1),y(0),A.current&&(A.current.value=""),N&&setTimeout(()=>k(null),5e3)}}},P=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?j(!0):"dragleave"===e.type&&j(!1)},[]),S=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),j(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&C(e.dataTransfer.files)},[e]),M=async e=>{if(confirm("Are you sure you want to delete this document?")){i(t=>t.filter(t=>t.id!==e));try{if(!(await fetch(`/api/documents/${e}`,{method:"DELETE"})).ok)throw i(s),Error("Failed to delete document");k("Document deleted successfully"),await _(),setTimeout(()=>k(null),3e3)}catch(e){i(s),w(`Delete failed: ${e.message}`),setTimeout(()=>w(null),8e3)}}},T=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},E=e=>e.includes("pdf")?(0,r.jsx)(n,{className:"w-5 h-5 text-red-500"}):e.includes("word")?(0,r.jsx)(n,{className:"w-5 h-5 text-blue-500"}):(0,r.jsx)(l,{className:"w-5 h-5 text-gray-500"});return(0,r.jsxs)("div",{className:"space-y-6",children:[b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:b})]})}),N&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("p",{className:"text-green-800 text-sm font-medium",children:N})]})}),(0,r.jsxs)("div",{className:`relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ${v?"border-orange-400 bg-orange-50 scale-105 shadow-lg":"border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md"} ${!e?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDragEnter:P,onDragLeave:P,onDragOver:P,onDrop:S,onClick:()=>e&&A.current?.click(),children:[(0,r.jsx)("input",{ref:A,type:"file",className:"hidden",accept:".pdf,.txt,.md",onChange:e=>{e.target.files&&e.target.files[0]&&C(e.target.files)},disabled:!e||m}),m?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c,{className:"w-12 h-12 text-orange-500 mx-auto animate-spin"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Processing Document..."}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:`${f}%`}})}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[f,"% complete"]})]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(u,{className:"w-12 h-12 text-gray-400 mx-auto"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-900",children:e?"Upload Knowledge Documents":"Select a configuration first"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Drag and drop files here, or click to browse"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Supports PDF, TXT, MD files up to 10MB"})]})]})]}),s.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Uploaded Documents"}),h&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(c,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:"Refreshing..."})]})]}),(0,r.jsx)("div",{className:"grid gap-4",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[E(e.file_type),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.filename}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[T(e.file_size)," • ",e.chunks_count," chunks"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["completed"===e.status&&(0,r.jsx)(d,{className:"w-5 h-5 text-green-500"}),"processing"===e.status&&(0,r.jsx)(c,{className:"w-5 h-5 text-orange-500 animate-spin"}),"failed"===e.status&&(0,r.jsx)(o.A,{className:"w-5 h-5 text-red-500"}),(0,r.jsx)("span",{className:`text-sm font-medium ${"completed"===e.status?"text-green-600":"processing"===e.status?"text-orange-600":"text-red-600"}`,children:"completed"===e.status?"Ready":"processing"===e.status?"Processing":"Failed"})]}),(0,r.jsx)("button",{onClick:()=>M(e.id),className:"p-1 text-gray-400 hover:text-red-500 transition-colors",title:"Delete document",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})})]})]},e.id))})]})]})}function x(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(""),[n,l]=(0,a.useState)([]),[o,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(null),[p,x]=(0,a.useState)(null),[h,g]=(0,a.useState)(""),f=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let s of e.split("\n").filter(e=>e.trim())){let e=s.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let s=e.includes("→")?"→":"->",r=e.split(s);if(r.length>=2){let e=r[0].trim(),a=r.slice(1).join(s).trim();t.examples.push({input:e,output:a})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},y=async()=>{if(!s||!h.trim())return void u("Please select an API configuration and provide training prompts.");if(!o){d(!0),u(null),x(null);try{let t=f(h),r=e.find(e=>e.id===s)?.name||"Unknown Config",a={custom_api_config_id:s,name:`${r} Training - ${new Date().toLocaleDateString()}`,description:`Training job for ${r} with ${t.examples.length} examples`,training_data:{processed_prompts:t,raw_prompts:h.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},i=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!i.ok){let e=await i.text();throw Error(`Failed to save training job: ${i.status} ${e}`)}let n=await i.json(),l="updated"===n.operation,o=`${l?"\uD83D\uDD04":"\uD83C\uDF89"} Prompt Engineering ${l?"updated":"completed"} successfully!

Your "${r}" configuration has been ${l?"updated":"enhanced"} with:
• ${t.examples.length} training examples
• Custom system instructions and behavior guidelines

✨ All future chats using this configuration will automatically:
• Follow your training examples
• Apply your behavior guidelines
• Maintain consistent personality and responses

🚀 Try it now in the Playground to see your ${l?"updated":"enhanced"} model in action!

💡 Your training prompts remain here so you can modify them anytime.`;x(o)}catch(e){u(`Failed to create prompt engineering: ${e.message}`)}finally{d(!1)}}};return(0,r.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"AI Training & Enhancement"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants."})]}),c&&(0,r.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:c})]})}),p&&(0,r.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,r.jsx)("p",{className:"text-green-800 text-sm font-medium",children:p})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Knowledge Documents"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Upload documents to enhance your AI with proprietary knowledge"})]})]}),(0,r.jsx)(m,{configId:s,onDocumentUploaded:()=>{}})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Custom Prompts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Define behavior, examples, and instructions for your AI"})]})]}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),(0,r.jsxs)("select",{id:"configSelect",value:s,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[(0,r.jsx)("option",{value:"",children:"Choose which model to train..."}),e.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),(0,r.jsx)("textarea",{id:"trainingPrompts",value:h,onChange:e=>g(e.target.value),placeholder:`Enter your training prompts using these formats:

SYSTEM: You are a helpful customer service agent for our company
BEHAVIOR: Always be polite and offer solutions

User asks about returns → I'd be happy to help with your return! Let me check our policy for you.
Customer is frustrated → I understand your frustration. Let me see how I can resolve this for you.

General instructions can be written as regular text.`,rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),(0,r.jsxs)("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),(0,r.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[(0,r.jsx)("div",{className:"flex space-x-3",children:(0,r.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{confirm("Clear all training prompts?")&&g("")},children:"Clear Form"})}),(0,r.jsx)("button",{type:"button",onClick:y,disabled:!s||!h.trim()||o,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79729:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92339:(e,t,s)=>{Promise.resolve().then(s.bind(s,79729))},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7482,4912],()=>s(38149));module.exports=r})();