(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5690],{38152:(e,a,s)=>{"use strict";s.d(a,{Pi:()=>t.A,fK:()=>i.A,uc:()=>r.A});var t=s(55628),r=s(31151),i=s(74500)},68492:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>u});var t=s(95155),r=s(12115),i=s(6874),l=s.n(i),n=s(72227),c=s(94038),o=s(61316),d=s(85037),m=s(31151),x=s(80377),h=s(87162),f=s(74338),g=s(28003);function u(){let[e,a]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0),[u,j]=(0,r.useState)(null),[y,N]=(0,r.useState)(""),[b,p]=(0,r.useState)(!1),w=(0,h.Z)(),[v,C]=(0,r.useState)(!1),{createHoverPrefetch:A,prefetchManageKeysData:k}=(0,g._)(),M=async()=>{i(!0),j(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch configurations")}let s=await e.json();a(s)}catch(e){j(e.message)}finally{i(!1)}};(0,r.useEffect)(()=>{M()},[]);let S=async e=>{if(e.preventDefault(),!y.trim())return void j("Configuration name cannot be empty.");p(!0),j(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:y})}),a=await e.json();if(!e.ok)throw Error(a.details||a.error||"Failed to create configuration");N(""),C(!1),await M()}catch(e){j(e.message)}finally{p(!1)}},T=(e,a)=>{w.showConfirmation({title:"Delete Configuration",message:'Are you sure you want to delete "'.concat(a,'"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.'),confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{j(null);try{let a=await fetch("/api/custom-configs/".concat(e),{method:"DELETE"}),s=await a.json();if(!a.ok)throw Error(s.details||s.error||"Failed to delete configuration");await M()}catch(e){throw j("Failed to delete: ".concat(e.message)),e}})};return(0,t.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,t.jsxs)("button",{onClick:()=>C(!v),className:v?"btn-secondary":"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),v?"Cancel":"Create New Model"]})]}),u&&(0,t.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("p",{className:"text-red-800",children:u})]})}),v&&(0,t.jsxs)("div",{className:"card max-w-md animate-scale-in p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),(0,t.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),(0,t.jsx)("input",{type:"text",id:"configName",value:y,onChange:e=>N(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),(0,t.jsx)("button",{type:"submit",disabled:b,className:"btn-primary w-full",children:b?"Creating...":"Create Model"})]})]}),s&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,a)=>(0,t.jsx)(f.B0,{},a))}),!s&&!e.length&&!u&&!v&&(0,t.jsx)("div",{className:"card text-center py-12",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,t.jsx)(c.A,{className:"h-8 w-8 text-orange-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,t.jsxs)("button",{onClick:()=>C(!0),className:"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})]})}),!s&&e.length>0&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,a)=>(0,t.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(100*a,"ms")},children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(c.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:(0,t.jsx)(c.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,t.jsx)(l(),{href:"/my-models/".concat(e.id),className:"flex-1",...A(e.id),children:(0,t.jsxs)("button",{className:"btn-primary w-full",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,t.jsxs)("button",{onClick:()=>T(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,t.jsx)(x.A,{isOpen:w.isOpen,onClose:w.hideConfirmation,onConfirm:w.onConfirm,title:w.title,message:w.message,confirmText:w.confirmText,cancelText:w.cancelText,type:w.type,isLoading:w.isLoading})]})}},69528:(e,a,s)=>{Promise.resolve().then(s.bind(s,68492))}},e=>{var a=a=>e(e.s=a);e.O(0,[5738,9968,6060,4609,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(69528)),_N_E=e.O()}]);