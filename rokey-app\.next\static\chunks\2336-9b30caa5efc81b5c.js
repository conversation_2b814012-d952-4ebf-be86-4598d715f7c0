"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2336],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6101:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>i});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function l(...e){return r.useCallback(i(...e),e)}},15613:(e,t,n)=>{n.d(t,{L:()=>r});let r=!1},22475:(e,t,n)=>{n.d(t,{UE:()=>ea,ll:()=>er,rD:()=>eu,UU:()=>el,cY:()=>eo,BN:()=>ei});let r=Math.min,o=Math.max,i=Math.round,l=Math.floor,a=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function f(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(d(e))?"y":"x"}function h(e){return e.replace(/start|end/g,e=>c[e])}function g(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function y(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function w(e,t,n){let r,{reference:o,floating:i}=e,l=v(t),a=p(v(t)),u=m(a),c=d(t),s="y"===l,h=o.x+o.width/2-i.width/2,g=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:h,y:o.y-i.height};break;case"bottom":r={x:h,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-i.width,y:g};break;default:r={x:o.x,y:o.y}}switch(f(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let x=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=w(c,r,u),f=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:v}=a[n],{x:h,y:g,data:y,reset:b}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=h?h:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:d}=w(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function E(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=s(t,e),v=y(m),h=a[p?"floating"===f?"reference":"floating":f],g=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:u})),w="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},R=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:u}):w);return{top:(g.top-R.top+v.top)/E.y,bottom:(R.bottom-g.bottom+v.bottom)/E.y,left:(g.left-R.left+v.left)/E.x,right:(R.right-g.right+v.right)/E.x}}async function R(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=d(n),a=f(n),u="y"===v(n),c=["left","top"].includes(l)?-1:1,p=i&&u?-1:1,m=s(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),u?{x:g*p,y:h*c}:{x:h*c,y:g*p}}function k(){return"undefined"!=typeof window}function C(e){return A(e)?(e.nodeName||"").toLowerCase():"#document"}function L(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(A(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function A(e){return!!k()&&(e instanceof Node||e instanceof L(e).Node)}function T(e){return!!k()&&(e instanceof Element||e instanceof L(e).Element)}function M(e){return!!k()&&(e instanceof HTMLElement||e instanceof L(e).HTMLElement)}function S(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof L(e).ShadowRoot)}function D(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=F(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function O(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function P(e){let t=j(),n=T(e)?F(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function j(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function I(e){return["html","body","#document"].includes(C(e))}function F(e){return L(e).getComputedStyle(e)}function W(e){return T(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===C(e))return e;let t=e.assignedSlot||e.parentNode||S(e)&&e.host||N(e);return S(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=_(t);return I(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&D(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=L(o);if(i){let e=z(l);return t.concat(l,l.visualViewport||[],D(o)?o:[],e&&n?B(e):[])}return t.concat(o,B(o,[],n))}function z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function H(e){let t=F(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=M(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=i(n)!==l||i(r)!==a;return u&&(n=l,r=a),{width:n,height:r,$:u}}function V(e){return T(e)?e:e.contextElement}function U(e){let t=V(e);if(!M(t))return a(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=H(t),u=(l?i(n.width):n.width)/r,c=(l?i(n.height):n.height)/o;return u&&Number.isFinite(u)||(u=1),c&&Number.isFinite(c)||(c=1),{x:u,y:c}}let G=a(0);function $(e){let t=L(e);return j()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function q(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=V(e),u=a(1);t&&(r?T(r)&&(u=U(r)):u=U(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===L(l))&&o)?$(l):a(0),s=(i.left+c.x)/u.x,d=(i.top+c.y)/u.y,f=i.width/u.x,p=i.height/u.y;if(l){let e=L(l),t=r&&T(r)?L(r):r,n=e,o=z(n);for(;o&&r&&t!==n;){let e=U(o),t=o.getBoundingClientRect(),r=F(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=z(n=L(o))}}return b({width:f,height:p,x:s,y:d})}function K(e,t){let n=W(e).scrollLeft;return t?t.left+n:q(N(e)).left+n}function X(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function Y(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=L(e),r=N(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=j();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=W(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),u=-n.scrollTop;return"rtl"===F(r).direction&&(a+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:a,y:u}}(N(e));else if(T(t))r=function(e,t){let n=q(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=M(e)?U(e):a(1),l=e.clientWidth*i.x,u=e.clientHeight*i.y;return{width:l,height:u,x:o*i.x,y:r*i.y}}(t,n);else{let n=$(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function Z(e){return"static"===F(e).position}function J(e,t){if(!M(e)||"fixed"===F(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function Q(e,t){let n=L(e);if(O(e))return n;if(!M(e)){let t=_(e);for(;t&&!I(t);){if(T(t)&&!Z(t))return t;t=_(t)}return n}let r=J(e,t);for(;r&&["table","td","th"].includes(C(r))&&Z(r);)r=J(r,t);return r&&I(r)&&Z(r)&&!P(r)?n:r||function(e){let t=_(e);for(;M(t)&&!I(t);){if(P(t))return t;if(O(t))break;t=_(t)}return null}(e)||n}let ee=async function(e){let t=this.getOffsetParent||Q,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),o=N(t),i="fixed"===n,l=q(e,!0,i,t),u={scrollLeft:0,scrollTop:0},c=a(0);if(r||!r&&!i)if(("body"!==C(t)||D(o))&&(u=W(t)),r){let e=q(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=K(o));i&&!r&&o&&(c.x=K(o));let s=!o||r||i?a(0):X(o,u);return{x:l.left+u.scrollLeft-c.x-s.x,y:l.top+u.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},et={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=N(r),u=!!t&&O(t.floating);if(r===l||u&&i)return n;let c={scrollLeft:0,scrollTop:0},s=a(1),d=a(0),f=M(r);if((f||!f&&!i)&&(("body"!==C(r)||D(l))&&(c=W(r)),M(r))){let e=q(r);s=U(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?a(0):X(l,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-c.scrollTop*s.y+d.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:l}=e,a=[..."clippingAncestors"===n?O(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>T(e)&&"body"!==C(e)),o=null,i="fixed"===F(e).position,l=i?_(e):e;for(;T(l)&&!I(l);){let t=F(l),n=P(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||D(l)&&!n&&function e(t,n){let r=_(t);return!(r===n||!T(r)||I(r))&&("fixed"===F(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=_(l)}return t.set(e,r),r}(t,this._c):[].concat(n),i],u=a[0],c=a.reduce((e,n)=>{let i=Y(t,n,l);return e.top=o(i.top,e.top),e.right=r(i.right,e.right),e.bottom=r(i.bottom,e.bottom),e.left=o(i.left,e.left),e},Y(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:Q,getElementRects:ee,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=H(e);return{width:t,height:n}},getScale:U,isElement:T,isRTL:function(e){return"rtl"===F(e).direction}};function en(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function er(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=i,p=V(e),m=u||c?[...p?B(p):[],...B(t)]:[];m.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,i=null,a=N(e);function u(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),u();let f=e.getBoundingClientRect(),{left:p,top:m,width:v,height:h}=f;if(s||t(),!v||!h)return;let g=l(m),y=l(a.clientWidth-(p+v)),b={rootMargin:-g+"px "+-y+"px "+-l(a.clientHeight-(m+h))+"px "+-l(p)+"px",threshold:o(0,r(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||en(f,e.getBoundingClientRect())||c(),w=!1}try{i=new IntersectionObserver(x,{...b,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(x,b)}i.observe(e)}(!0),u}(p,n):null,h=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?q(e):null;return f&&function t(){let r=q(e);y&&!en(y,r)&&n(),y=r,a=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(a)}}let eo=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await R(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}},ei=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=s(e,t),m={x:n,y:i},h=await E(t,f),g=v(d(l)),y=p(g),b=m[y],w=m[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+h[e],i=b-h[t];b=o(n,r(b,i))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],i=w-h[t];w=o(n,r(w,i))}let x=c.fn({...t,[y]:b,[g]:w});return{...x,data:{x:x.x-n,y:x.y-i,enabled:{[y]:a,[g]:u}}}}}},el=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:y,platform:b,elements:w}=t,{mainAxis:x=!0,crossAxis:R=!0,fallbackPlacements:k,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:N=!0,...A}=s(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let T=d(a),M=v(y),S=d(y)===y,D=await (null==b.isRTL?void 0:b.isRTL(w.floating)),O=k||(S||!N?[g(y)]:function(e){let t=g(e);return[h(e),t,h(t)]}(y)),P="none"!==L;!k&&P&&O.push(...function(e,t,n,r){let o=f(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(h)))),i}(y,N,L,D));let j=[y,...O],I=await E(t,A),F=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&F.push(I[T]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=f(e),o=p(v(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=g(l)),[l,g(l)]}(a,c,D);F.push(I[e[0]],I[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=j[e];if(t&&("alignment"!==R||M===v(t)||W.every(e=>e.overflows[0]>0&&v(e.placement)===M)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=W.filter(e=>{if(P){let t=v(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=y}if(a!==n)return{reset:{placement:n}}}return{}}}},ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:l,rects:a,platform:u,elements:c,middlewareData:d}=t,{element:h,padding:g=0}=s(e,t)||{};if(null==h)return{};let b=y(g),w={x:n,y:i},x=p(v(l)),E=m(x),R=await u.getDimensions(h),k="y"===x,C=k?"clientHeight":"clientWidth",L=a.reference[E]+a.reference[x]-w[x]-a.floating[E],N=w[x]-a.reference[x],A=await (null==u.getOffsetParent?void 0:u.getOffsetParent(h)),T=A?A[C]:0;T&&await (null==u.isElement?void 0:u.isElement(A))||(T=c.floating[C]||a.floating[E]);let M=T/2-R[E]/2-1,S=r(b[k?"top":"left"],M),D=r(b[k?"bottom":"right"],M),O=T-R[E]-D,P=T/2-R[E]/2+(L/2-N/2),j=o(S,r(P,O)),I=!d.arrow&&null!=f(l)&&P!==j&&a.reference[E]/2-(P<S?S:D)-R[E]/2<0,F=I?P<S?P-S:P-O:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:P-j-F,...I&&{alignmentOffset:F}},reset:I}}}),eu=(e,t,n)=>{let r=new Map,o={platform:et,...n},i={...o.platform,_c:r};return x(e,t,{...o,platform:i})}},24357:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),i=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},34869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},40968:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(12115),o=n(63655),i=n(95155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},46081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(12115),o=n(95155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},52589:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(12115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},i),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},53904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63655:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(12115),o=n(47650),i=n(99708),l=n(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},65529:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(12115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},i),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},67695:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(12115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},i),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))})},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},75525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},77484:(e,t,n)=>{n.d(t,{C1:()=>E,bL:()=>w});var r=n(12115),o=n(6101),i=n(46081),l=n(85185),a=n(85258),u=n(52712),c=n(28905),s=n(63655),d=n(95155),f="Checkbox",[p,m]=(0,i.A)(f),[v,h]=p(f);function g(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:i,disabled:l,form:u,name:c,onCheckedChange:s,required:p,value:m="on",internal_do_not_use_render:h}=e,[g,y]=(0,a.i)({prop:n,defaultProp:null!=i&&i,onChange:s,caller:f}),[b,w]=r.useState(null),[x,E]=r.useState(null),R=r.useRef(!1),k=!b||!!u||!!b.closest("form"),L={checked:g,disabled:l,setChecked:y,control:b,setControl:w,name:c,form:u,value:m,hasConsumerStoppedPropagationRef:R,required:p,defaultChecked:!C(i)&&i,isFormControl:k,bubbleInput:x,setBubbleInput:E};return(0,d.jsx)(v,{scope:t,...L,children:"function"==typeof h?h(L):o})}var y="CheckboxTrigger",b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:i,onClick:a,...u}=e,{control:c,value:f,disabled:p,checked:m,required:v,setControl:g,setChecked:b,hasConsumerStoppedPropagationRef:w,isFormControl:x,bubbleInput:E}=h(y,n),R=(0,o.s)(t,g),k=r.useRef(m);return r.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>b(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,b]),(0,d.jsx)(s.sG.button,{type:"button",role:"checkbox","aria-checked":C(m)?"mixed":m,"aria-required":v,"data-state":L(m),"data-disabled":p?"":void 0,disabled:p,value:f,...u,ref:R,onKeyDown:(0,l.m)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(a,e=>{b(e=>!!C(e)||!e),E&&x&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})})});b.displayName=y;var w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:l,disabled:a,value:u,onCheckedChange:c,form:s,...f}=e;return(0,d.jsx)(g,{__scopeCheckbox:n,checked:o,defaultChecked:i,disabled:a,required:l,onCheckedChange:c,name:r,form:s,value:u,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(b,{...f,ref:t,__scopeCheckbox:n}),r&&(0,d.jsx)(k,{__scopeCheckbox:n})]})}})});w.displayName=f;var x="CheckboxIndicator",E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=h(x,n);return(0,d.jsx)(c.C,{present:r||C(i.checked)||!0===i.checked,children:(0,d.jsx)(s.sG.span,{"data-state":L(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=x;var R="CheckboxBubbleInput",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...i}=e,{control:l,hasConsumerStoppedPropagationRef:a,checked:c,defaultChecked:f,required:p,disabled:m,name:v,value:g,form:y,bubbleInput:b,setBubbleInput:w}=h(R,n),x=(0,o.s)(t,w),E=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(c),k=function(e){let[t,n]=r.useState(void 0);return(0,u.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(l);r.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(E!==c&&e){let n=new Event("click",{bubbles:t});b.indeterminate=C(c),e.call(b,!C(c)&&c),b.dispatchEvent(n)}},[b,E,c,a]);let L=r.useRef(!C(c)&&c);return(0,d.jsx)(s.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:L.current,required:p,disabled:m,name:v,value:g,form:y,...i,tabIndex:-1,ref:x,style:{...i.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function L(e){return C(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=R},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},79397:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},85258:(e,t,n)=>{n.d(t,{i:()=>a});var r=n(12115),o=n.t(r,2),i=n(52712),l=(o[" useEffectEvent ".trim().toString()],o[" useInsertionEffect ".trim().toString()],o[" useInsertionEffect ".trim().toString()]||i.N);function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,o]=r.useState(e),i=r.useRef(n),a=r.useRef(t);return l(()=>{a.current=t},[t]),r.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,o,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=r.useRef(void 0!==e);r.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,o])}return[s,r.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},87489:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(12115),o=n(63655),i=n(95155),l="horizontal",a=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=l,...c}=e,s=(n=u,a.includes(n))?u:l;return(0,i.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});u.displayName="Separator";var c=u},89959:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(12115);let o=r.forwardRef(function(e,t){let{title:n,titleId:o,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":o},i),n?r.createElement("title",{id:o},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9.75v6.75m0 0-3-3m3 3 3-3m-8.25 6a4.5 4.5 0 0 1-1.41-8.775 5.25 5.25 0 0 1 10.233-2.33 3 3 0 0 1 3.758 3.848A3.752 3.752 0 0 1 18 19.5H6.75Z"}))})},90029:(e,t,n)=>{n.d(t,{bm:()=>e3,UC:()=>e4,VY:()=>e7,hJ:()=>e5,ZL:()=>e2,bL:()=>e0,hE:()=>e9,l9:()=>e1});var r,o,i=n(12115),l=n.t(i,2),a=n(85185),u=n(6101),c=n(46081),s=n(52712),d=l[" useId ".trim().toString()]||(()=>void 0),f=0;function p(e){let[t,n]=i.useState(d());return(0,s.N)(()=>{e||n(e=>e??String(f++))},[e]),e||(t?`radix-${t}`:"")}var m=n(85258),v=n(63655);function h(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var g=n(95155),y="dismissableLayer.update",b=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=i.forwardRef((e,t)=>{var n,o;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:c,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:f,onDismiss:p,...m}=e,w=i.useContext(b),[R,k]=i.useState(null),C=null!=(o=null==R?void 0:R.ownerDocument)?o:null==(n=globalThis)?void 0:n.document,[,L]=i.useState({}),N=(0,u.s)(t,e=>k(e)),A=Array.from(w.layers),[T]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),M=A.indexOf(T),S=R?A.indexOf(R):-1,D=w.layersWithOutsidePointerEventsDisabled.size>0,O=S>=M,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=h(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){E("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));O&&!n&&(null==s||s(e),null==f||f(e),e.defaultPrevented||null==p||p())},C),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=h(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&E("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},C);return!function(e,t=globalThis?.document){let n=h(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S===w.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},C),i.useEffect(()=>{if(R)return l&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(R)),w.layers.add(R),x(),()=>{l&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[R,C,l,w]),i.useEffect(()=>()=>{R&&(w.layers.delete(R),w.layersWithOutsidePointerEventsDisabled.delete(R),x())},[R,w]),i.useEffect(()=>{let e=()=>L({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,g.jsx)(v.sG.div,{...m,ref:N,style:{pointerEvents:D?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function x(){let e=new CustomEvent(y);document.dispatchEvent(e)}function E(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,v.hO)(i,l):i.dispatchEvent(l)}w.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(b),r=i.useRef(null),o=(0,u.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,g.jsx)(v.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var R="focusScope.autoFocusOnMount",k="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},L=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...a}=e,[c,s]=i.useState(null),d=h(o),f=h(l),p=i.useRef(null),m=(0,u.s)(t,e=>s(e)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(y.paused||!c)return;let t=e.target;c.contains(t)?p.current=t:T(p.current,{select:!0})},t=function(e){if(y.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||T(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&T(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,y.paused]),i.useEffect(()=>{if(c){M.add(y);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(R,C);c.addEventListener(R,d),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(T(r,{select:t}),document.activeElement!==n)return}(N(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&T(c))}return()=>{c.removeEventListener(R,d),setTimeout(()=>{let t=new CustomEvent(k,C);c.addEventListener(k,f),c.dispatchEvent(t),t.defaultPrevented||T(null!=e?e:document.body,{select:!0}),c.removeEventListener(k,f),M.remove(y)},0)}}},[c,d,f,y]);let b=i.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=N(e);return[A(t,e),A(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&T(i,{select:!0})):(e.preventDefault(),n&&T(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,g.jsx)(v.sG.div,{tabIndex:-1,...a,ref:m,onKeyDown:b})});function N(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function A(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function T(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}L.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=S(e,t)).unshift(t)},remove(t){var n;null==(n=(e=S(e,t))[0])||n.resume()}}}();function S(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var D=n(47650),O=i.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[a,u]=i.useState(!1);(0,s.N)(()=>u(!0),[]);let c=o||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?D.createPortal((0,g.jsx)(v.sG.div,{...l,ref:t}),c):null});O.displayName="Portal";var P=n(28905),j=0;function I(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var F=n(39249),W="right-scroll-bar-position",_="width-before-scroll-bar",B=n(8325),z=n(8812),H=(0,z.fi)(),V=function(){},U=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:V,onWheelCapture:V,onTouchMoveCapture:V}),o=r[0],l=r[1],a=e.forwardProps,u=e.children,c=e.className,s=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,m=e.noRelative,v=e.noIsolation,h=e.inert,g=e.allowPinchZoom,y=e.as,b=e.gapMode,w=(0,F.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=(0,B.SV)([n,t]),E=(0,F.Cl)((0,F.Cl)({},w),o);return i.createElement(i.Fragment,null,d&&i.createElement(p,{sideCar:H,removeScrollBar:s,shards:f,noRelative:m,noIsolation:v,inert:h,setCallbacks:l,allowPinchZoom:!!g,lockRef:n,gapMode:b}),a?i.cloneElement(i.Children.only(u),(0,F.Cl)((0,F.Cl)({},E),{ref:x})):i.createElement(void 0===y?"div":y,(0,F.Cl)({},E,{className:c,ref:x}),u))});U.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},U.classNames={fullWidth:_,zeroRight:W};var G=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},$=function(){var e=G();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},q=function(){var e=$();return function(t){return e(t.styles,t.dynamic),null}},K={left:0,top:0,right:0,gap:0},X=function(e){return parseInt(e||"",10)||0},Y=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[X(n),X(r),X(o)]},Z=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return K;var t=Y(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},J=q(),Q="data-scroll-locked",ee=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(Q,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(W," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(_," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(W," .").concat(W," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(_," .").concat(_," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Q,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},et=function(){var e=parseInt(document.body.getAttribute(Q)||"0",10);return isFinite(e)?e:0},en=function(){i.useEffect(function(){return document.body.setAttribute(Q,(et()+1).toString()),function(){var e=et()-1;e<=0?document.body.removeAttribute(Q):document.body.setAttribute(Q,e.toString())}},[])},er=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;en();var l=i.useMemo(function(){return Z(o)},[o]);return i.createElement(J,{styles:ee(l,!t,o,n?"":"!important")})},eo=!1;if("undefined"!=typeof window)try{var ei=Object.defineProperty({},"passive",{get:function(){return eo=!0,!0}});window.addEventListener("test",ei,ei),window.removeEventListener("test",ei,ei)}catch(e){eo=!1}var el=!!eo&&{passive:!1},ea=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},eu=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ec(e,r)){var o=es(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ec=function(e,t){return"v"===e?ea(t,"overflowY"):ea(t,"overflowX")},es=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ed=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var m=es(e,u),v=m[0],h=m[1]-m[2]-l*v;(v||h)&&ec(e,u)&&(f+=h,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},ef=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ep=function(e){return[e.deltaX,e.deltaY]},em=function(e){return e&&"current"in e?e.current:e},ev=0,eh=[];let eg=(0,z.mb)(H,function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(ev++)[0],l=i.useState(q)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,F.fX)([e.lockRef.current],(e.shards||[]).map(em),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=ef(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=eu(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=eu(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return ed(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(eh.length&&eh[eh.length-1]===l){var n="deltaY"in e?ep(e):ef(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(em).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=i.useCallback(function(e){n.current=ef(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,ep(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,ef(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return eh.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,el),document.addEventListener("touchmove",c,el),document.addEventListener("touchstart",d,el),function(){eh=eh.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,el),document.removeEventListener("touchmove",c,el),document.removeEventListener("touchstart",d,el)}},[]);var m=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(er,{noRelative:e.noRelative,gapMode:e.gapMode}):null)});var ey=i.forwardRef(function(e,t){return i.createElement(U,(0,F.Cl)({},e,{ref:t,sideCar:eg}))});ey.classNames=U.classNames;var eb=n(38168),ew=n(99708),ex="Dialog",[eE,eR]=(0,c.A)(ex),[ek,eC]=eE(ex),eL=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:l,modal:a=!0}=e,u=i.useRef(null),c=i.useRef(null),[s,d]=(0,m.i)({prop:r,defaultProp:null!=o&&o,onChange:l,caller:ex});return(0,g.jsx)(ek,{scope:t,triggerRef:u,contentRef:c,contentId:p(),titleId:p(),descriptionId:p(),open:s,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(e=>!e),[d]),modal:a,children:n})};eL.displayName=ex;var eN="DialogTrigger",eA=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eC(eN,n),i=(0,u.s)(t,o.triggerRef);return(0,g.jsx)(v.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eK(o.open),...r,ref:i,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});eA.displayName=eN;var eT="DialogPortal",[eM,eS]=eE(eT,{forceMount:void 0}),eD=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,l=eC(eT,t);return(0,g.jsx)(eM,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,g.jsx)(P.C,{present:n||l.open,children:(0,g.jsx)(O,{asChild:!0,container:o,children:e})}))})};eD.displayName=eT;var eO="DialogOverlay",eP=i.forwardRef((e,t)=>{let n=eS(eO,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eC(eO,e.__scopeDialog);return i.modal?(0,g.jsx)(P.C,{present:r||i.open,children:(0,g.jsx)(eI,{...o,ref:t})}):null});eP.displayName=eO;var ej=(0,ew.TL)("DialogOverlay.RemoveScroll"),eI=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eC(eO,n);return(0,g.jsx)(ey,{as:ej,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(v.sG.div,{"data-state":eK(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eF="DialogContent",eW=i.forwardRef((e,t)=>{let n=eS(eF,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=eC(eF,e.__scopeDialog);return(0,g.jsx)(P.C,{present:r||i.open,children:i.modal?(0,g.jsx)(e_,{...o,ref:t}):(0,g.jsx)(eB,{...o,ref:t})})});eW.displayName=eF;var e_=i.forwardRef((e,t)=>{let n=eC(eF,e.__scopeDialog),r=i.useRef(null),o=(0,u.s)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return(0,eb.Eq)(e)},[]),(0,g.jsx)(ez,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),eB=i.forwardRef((e,t)=>{let n=eC(eF,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,g.jsx)(ez,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,l;null==(i=e.onCloseAutoFocus)||i.call(e,t),t.defaultPrevented||(r.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var i,l;null==(i=e.onInteractOutside)||i.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),ez=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:l,...a}=e,c=eC(eF,n),s=i.useRef(null),d=(0,u.s)(t,s);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:I()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:I()),j++,()=>{1===j&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),j--}},[]),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(L,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:l,children:(0,g.jsx)(w,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":eK(c.open),...a,ref:d,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(eJ,{titleId:c.titleId}),(0,g.jsx)(eQ,{contentRef:s,descriptionId:c.descriptionId})]})]})}),eH="DialogTitle",eV=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eC(eH,n);return(0,g.jsx)(v.sG.h2,{id:o.titleId,...r,ref:t})});eV.displayName=eH;var eU="DialogDescription",eG=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eC(eU,n);return(0,g.jsx)(v.sG.p,{id:o.descriptionId,...r,ref:t})});eG.displayName=eU;var e$="DialogClose",eq=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eC(e$,n);return(0,g.jsx)(v.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function eK(e){return e?"open":"closed"}eq.displayName=e$;var eX="DialogTitleWarning",[eY,eZ]=(0,c.q)(eX,{contentName:eF,titleName:eH,docsSlug:"dialog"}),eJ=e=>{let{titleId:t}=e,n=eZ(eX),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&document.getElementById(t)},[r,t]),null},eQ=e=>{let{contentRef:t,descriptionId:n}=e,r=eZ("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&document.getElementById(n)},[o,t,n]),null},e0=eL,e1=eA,e2=eD,e5=eP,e4=eW,e9=eV,e7=eG,e3=eq},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99708:(e,t,n)=>{n.d(t,{TL:()=>l});var r=n(12115),o=n(6101),i=n(95155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var l;let e,a,u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...l}=e,a=r.Children.toArray(o),c=a.find(u);if(c){let e=c.props.children,o=a.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);