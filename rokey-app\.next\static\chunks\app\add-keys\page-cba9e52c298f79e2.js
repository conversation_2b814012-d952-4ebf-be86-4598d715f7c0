(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5833],{38152:(e,a,t)=>{"use strict";t.d(a,{Pi:()=>l.A,fK:()=>r.A,uc:()=>s.A});var l=t(55628),s=t(31151),r=t(74500)},75922:(e,a,t)=>{"use strict";t.d(a,{MG:()=>l});let l=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76805:(e,a,t)=>{Promise.resolve().then(t.bind(t,81767))},81767:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>c});var l=t(95155),s=t(12115),r=t(75922),d=t(80377),o=t(87162);let i=r.MG.map(e=>({value:e.id,label:e.name})),n=e=>{let a=r.MG.find(a=>a.id===e);return a?a.models.map(e=>({value:e.id,label:e.name})):[]};function c(){var e;let a=(0,o.Z)(),[t,r]=(0,s.useState)((null==(e=i[0])?void 0:e.value)||"openai"),[c,p]=(0,s.useState)(""),[u,m]=(0,s.useState)(""),[h,x]=(0,s.useState)(""),[y,g]=(0,s.useState)(!1),[b,f]=(0,s.useState)(null),[v,j]=(0,s.useState)(null),[N,w]=(0,s.useState)([]),[k,A]=(0,s.useState)(!0),[S,I]=(0,s.useState)(null),P=async()=>{A(!0);let e=null;try{let e=await fetch("/api/keys");if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch keys")}let a=await e.json();w(a),f(null)}catch(e){f("Error fetching keys: ".concat(e.message))}A(!1)};(0,s.useEffect)(()=>{P()},[]),(0,s.useEffect)(()=>{var e;p((null==(e=n(t)[0])?void 0:e.value)||"")},[t]);let _=async e=>{e.preventDefault(),g(!0),f(null),j(null);try{var a;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:t,predefined_model_id:c,api_key_raw:u,label:h,custom_api_config_id:""})}),l=await e.json();if(!e.ok)throw Error(l.details||l.error||"Failed to save API key");j('API key "'.concat(h,'" saved successfully!')),r((null==(a=i[0])?void 0:a.value)||"openai"),p(""),m(""),x(""),await P()}catch(e){f(e.message),j(null)}g(!1)},C=(e,t)=>{a.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{I(e),f(null),j(null);try{let a=await fetch("/api/keys/".concat(e),{method:"DELETE"}),l=await a.json();if(!a.ok)throw Error(l.details||l.error||"Failed to delete API key");j('API key "'.concat(t,'" deleted successfully!')),w(a=>a.filter(a=>a.id!==e))}catch(e){throw f(e.message),j(null),e}finally{I(null)}})},E=n(t);return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Add API Key"}),(0,l.jsxs)("form",{onSubmit:_,className:"space-y-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-lg",children:[b&&(0,l.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Error: ",b]}),v&&(0,l.jsx)("p",{className:"text-green-400 bg-green-900/50 p-3 rounded-md",children:v}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-1",children:"Provider"}),(0,l.jsx)("select",{id:"provider",value:t,onChange:e=>r(e.target.value),className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",children:i.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"modelId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Model ID"}),(0,l.jsx)("select",{id:"modelId",value:c,onChange:e=>p(e.target.value),disabled:!E.length,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50",children:E.length>0?E.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value)):(0,l.jsx)("option",{value:"",disabled:!0,children:"Select a provider first or no models configured"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"apiKey",className:"block text-sm font-medium text-gray-300 mb-1",children:"API Key"}),(0,l.jsx)("input",{type:"password",id:"apiKey",value:u,onChange:e=>m(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your API key"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-1",children:"Label"}),(0,l.jsx)("input",{type:"text",id:"label",value:h,onChange:e=>x(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., My Personal OpenAI Key"})]}),(0,l.jsx)("button",{type:"submit",disabled:y,className:"w-full px-4 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 disabled:opacity-50 font-medium",children:y?"Saving...":"Save API Key"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Saved API Keys"}),k?(0,l.jsx)("p",{className:"text-gray-400",children:"Loading keys..."}):b&&0===N.length?(0,l.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Could not load keys: ",b.replace("Error fetching keys: ","")]}):0===N.length?(0,l.jsx)("p",{className:"text-gray-400",children:"No API keys saved yet."}):(0,l.jsx)("div",{className:"overflow-x-auto bg-gray-800 p-4 rounded-lg shadow-xl",children:(0,l.jsxs)("table",{className:"min-w-full text-sm text-left text-gray-300",children:[(0,l.jsx)("thead",{className:"text-xs text-gray-400 uppercase bg-gray-700",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Label"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Provider"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Predefined Model ID"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Status"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Created At"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Last Used"}),(0,l.jsx)("th",{scope:"col",className:"px-6 py-3",children:(0,l.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,l.jsx)("tbody",{children:N.map(e=>(0,l.jsxs)("tr",{className:"border-b border-gray-700 hover:bg-gray-700/50",children:[(0,l.jsx)("td",{className:"px-6 py-4 font-medium whitespace-nowrap text-white",children:e.label}),(0,l.jsx)("td",{className:"px-6 py-4",children:e.provider}),(0,l.jsx)("td",{className:"px-6 py-4",children:e.predefined_model_id}),(0,l.jsx)("td",{className:"px-6 py-4",children:(0,l.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full \n                        ".concat("active"===e.status?"bg-green-900 text-green-300":"inactive"===e.status?"bg-yellow-900 text-yellow-300":"bg-red-900 text-red-300"),children:e.status})}),(0,l.jsx)("td",{className:"px-6 py-4",children:new Date(e.created_at).toLocaleDateString()}),(0,l.jsx)("td",{className:"px-6 py-4",children:e.last_used_at?new Date(e.last_used_at).toLocaleDateString():"Never"}),(0,l.jsx)("td",{className:"px-6 py-4 text-right",children:(0,l.jsx)("button",{onClick:()=>C(e.id,e.label),disabled:S===e.id,className:"font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed",children:S===e.id?"Deleting...":"Delete"})})]},e.id))})]})})]}),(0,l.jsx)(d.A,{isOpen:a.isOpen,onClose:a.hideConfirmation,onConfirm:a.onConfirm,title:a.title,message:a.message,confirmText:a.confirmText,cancelText:a.cancelText,type:a.type,isLoading:a.isLoading})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[5738,9968,6060,4609,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(76805)),_N_E=e.O()}]);