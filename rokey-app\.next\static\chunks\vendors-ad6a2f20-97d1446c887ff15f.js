"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[563],{78:(t,e,n)=>{n.d(e,{w:()=>M});var i=n(52290),r=n(21448),s=n(43891),o=n(66698),a=n(51442),l=n(26953),u=n(51586),h=n(78588),c=n(64200),d=n(81786),m=n(94198),p=n(33757),f=n(68212),v=n(33991),g=n(76333),y=n(61665);function x(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function P(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function E(t,e,n){return{min:A(t,e),max:A(t,n)}}function A(t,e){return"number"==typeof t?t:t[e]||0}let w=new WeakMap;class S{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,d.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new y.Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,u.e)(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:r}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,s.Wp)(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,m.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(s.rq.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=(0,c.CQ)(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&s.Gt.postRender(()=>r(t,e)),(0,g.g)(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:r,onDrag:s}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,m.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:(0,f.s)(this.visualElement)})}stop(t,e){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:r}=this.getProps();r&&s.Gt.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!C(t,i,this.currentDirection))return;let r=this.getAxisMotionValue(t),o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?(0,s.k$)(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?(0,s.k$)(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),r.set(o)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&(0,v.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:r}){return{x:x(t.x,n,r),y:x(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:E(t,"left","right"),y:E(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&(0,m.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!(0,v.X)(e))return!1;let i=e.current;(0,r.V1)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let o=(0,p.L)(i,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:P(t.x,o.x),y:P(t.y,o.y)});if(n){let t=n((0,h.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,h.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all((0,m.X)(o=>{if(!C(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return(0,g.g)(this.visualElement,t),n.start((0,o.f)(t,n,0,e,this.visualElement,!1))}stopAnimation(){(0,m.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,m.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){(0,m.X)(e=>{let{drag:n}=this.getProps();if(!C(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,r=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:o}=i.layout.layoutBox[e];r.set(t[e]-(0,s.k$)(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!(0,v.X)(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};(0,m.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=(0,c.CQ)(t),s=(0,c.CQ)(e);return s>i?n=(0,r.qB)(e.min,e.max-i,t.min):i>s&&(n=(0,r.qB)(t.min,t.max-s,e.min)),(0,r.qE)(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),(0,m.X)(e=>{if(!C(e,t,null))return;let n=this.getAxisMotionValue(e),{min:r,max:o}=this.constraints[e];n.set((0,s.k$)(r,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;w.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),n=()=>{let{dragConstraints:t}=this.getProps();(0,v.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),s.Gt.read(n);let o=(0,a.k)(window,"resize",()=>this.scalePositionWithinConstraints()),u=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,m.X)(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{o(),e(),r(),u&&u()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function C(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class M extends i.X{constructor(t){super(t),this.removeGroupControls=r.lQ,this.removeListeners=r.lQ,this.controls=new S(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||r.lQ}unmount(){this.removeGroupControls(),this.removeListeners()}}},198:(t,e,n)=>{n(21448),n(18802),n(78660)},1265:(t,e,n)=>{n(43891),n(21448),n(31788),n(46926);let i=new Set},2736:(t,e,n)=>{n(95155),n(21448),n(12115),n(82885),n(43050)},2999:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(12115).createContext)({})},5910:(t,e,n)=>{n.d(e,{p:()=>i});let i=t=>Array.isArray(t)},6340:(t,e,n)=>{n.d(e,{N:()=>i});function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},16242:(t,e,n)=>{n(82885);class i{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}},19209:(t,e,n)=>{n.d(e,{Y:()=>h,q:()=>v});var i=n(95155),r=n(21448),s=n(12115);let o=(0,s.createContext)(null);var a=n(36545),l=n(82885),u=n(43891);let h=(0,s.forwardRef)(function(t,e){let{children:n,as:h="ul",axis:m="y",onReorder:p,values:f,...v}=t,g=(0,l.M)(()=>a.P[h]),y=[],x=(0,s.useRef)(!1);return(0,r.V1)(!!f,"Reorder.Group must be provided a values prop"),(0,s.useEffect)(()=>{x.current=!1}),(0,i.jsx)(g,{...v,ref:e,ignoreStrict:!0,children:(0,i.jsx)(o.Provider,{value:{axis:m,registerItem:(t,e)=>{let n=y.findIndex(e=>t===e.value);-1!==n?y[n].layout=e[m]:y.push({value:t,layout:e[m]}),y.sort(d)},updateOrder:(t,e,n)=>{if(x.current)return;let i=function(t,e,n,i){if(!i)return t;let s=t.findIndex(t=>t.value===e);if(-1===s)return t;let o=i>0?1:-1,a=t[s+o];if(!a)return t;let l=t[s],h=a.layout,c=(0,u.k$)(h.min,h.max,.5);return 1===o&&l.layout.max+n>c||-1===o&&l.layout.min+n<c?(0,r.Pe)(t,s,s+o):t}(y,t,e,n);y!==i&&(x.current=!0,p(i.map(c).filter(t=>-1!==f.indexOf(t))))}},children:n})})});function c(t){return t.value}function d(t,e){return t.layout.min-e.layout.min}var m=n(8619),p=n(62094);function f(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,u.SS)(t)?t:(0,m.d)(e)}let v=(0,s.forwardRef)(function(t,e){let{children:n,style:u={},value:h,as:c="li",onDrag:d,layout:m=!0,...v}=t,g=(0,l.M)(()=>a.P[c]),y=(0,s.useContext)(o),x={x:f(u.x),y:f(u.y)},P=(0,p.G)([x.x,x.y],t=>{let[e,n]=t;return e||n?1:"unset"});(0,r.V1)(!!y,"Reorder.Item must be a child of Reorder.Group");let{axis:E,registerItem:A,updateOrder:w}=y;return(0,i.jsx)(g,{drag:E,...v,dragSnapToOrigin:!0,style:{...u,x:x.x,y:x.y,zIndex:P},layout:m,onDrag:(t,e)=>{let{velocity:n}=e;n[E]&&w(h,x[E].get(),n[E]),d&&d(t,e)},onLayoutMeasure:t=>A(h,t),ref:e,ignoreStrict:!0,children:n})})},19578:(t,e,n)=>{n.d(e,{$:()=>l});var i=n(43891),r=n(18802),s=n(76333),o=n(46926),a=n(66698);function l(t,e,{delay:n=0,transitionOverride:u,type:h}={}){let{transition:c=t.getDefaultTransition(),transitionEnd:d,...m}=e;u&&(c=u);let p=[],f=h&&t.animationState&&t.animationState.getState()[h];for(let e in m){let r=t.getValue(e,t.latestValues[e]??null),l=m[e];if(void 0===l||f&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(f,e))continue;let u={delay:n,...(0,i.rU)(c||{},e)},h=r.get();if(void 0!==h&&!r.isAnimating&&!Array.isArray(l)&&l===h&&!u.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=(0,o.P)(t);if(n){let t=window.MotionHandoffAnimation(n,e,i.Gt);null!==t&&(u.startTime=t,d=!0)}}(0,s.g)(t,e),r.start((0,a.f)(e,r,l,t.shouldReduceMotion&&i.$y.has(e)?{type:!1}:u,t,d));let v=r.animation;v&&p.push(v)}return d&&Promise.all(p).then(()=>{i.Gt.update(()=>{d&&(0,r.U)(t,d)})}),p}},19624:(t,e,n)=>{n.d(e,{c:()=>o});var i=n(21448),r=n(51442),s=n(52290);class o extends s.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,i.Fs)((0,r.k)(this.node.current,"focus",()=>this.onFocus()),(0,r.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},19726:(t,e,n)=>{n.d(e,{e:()=>a});var i=n(43891),r=n(51586),s=n(52290);function o(t,e,n){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===n);let o=s["onHover"+n];o&&i.Gt.postRender(()=>o(e,(0,r.e)(e)))}class a extends s.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.PT)(t,(t,e)=>(o(this.node,e,"Start"),t=>o(this.node,t,"End"))))}unmount(){}}},24132:(t,e,n)=>{n.d(e,{z:()=>a});var i=n(12115),r=n(2999),s=n(19253),o=n(65305);function a(t){let{initial:e,animate:n}=function(t,e){if((0,s.e)(t)){let{initial:e,animate:n}=t;return{initial:!1===e||(0,o.w)(e)?e:void 0,animate:(0,o.w)(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(r.A));return(0,i.useMemo)(()=>({initial:e,animate:n}),[l(e),l(n)])}function l(t){return Array.isArray(t)?t.join(" "):t}},25214:(t,e,n)=>{n.d(e,{Y:()=>i});let i=(0,n(12115).createContext)({strict:!1})},26953:(t,e,n)=>{n.d(e,{h:()=>s});var i=n(51442),r=n(51586);function s(t,e,n,s){return(0,i.k)(t,e,(0,r.F)(n),s)}},31788:(t,e,n)=>{n.d(e,{n:()=>i});let i="data-"+(0,n(78450).I)("framerAppearId")},32082:(t,e,n)=>{n.d(e,{xQ:()=>s});var i=n(12115),r=n(80845);function s(t=!0){let e=(0,i.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:o,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!n&&o?[!1,u]:[!0]}},35580:(t,e,n)=>{n.d(e,{z:()=>s});var i=n(43891),r=n(66698);function s(t,e,n){let s=(0,i.SS)(t)?t:(0,i.OQ)(t);return s.start((0,r.f)("",s,e,n)),s.animation}},36464:(t,e,n)=>{var i=n(43891),r=n(21448);function s(t){return"object"==typeof t&&!Array.isArray(t)}let o=t=>"number"==typeof t;var a=n(65511),l=n(19578),u=n(75245),h=n(13513),c=n(60728);function d(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,i.xZ)(t)&&!(0,i.h1)(t)?new c.l(e):new u.M(e);n.mount(t),a.C.set(t,n)}function m(t){let e=new h.K({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),a.C.set(t,e)}var p=n(35580)},38160:(t,e,n)=>{n.d(e,{f:()=>h});var i=n(43891),r=n(21448),s=n(26953),o=n(52290),a=n(68212),l=n(61665);let u=t=>(e,n)=>{t&&i.Gt.postRender(()=>t(e,n))};class h extends o.X{constructor(){super(...arguments),this.removePointerDownListener=r.lQ}onPointerDown(t){this.session=new l.Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,a.s)(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:u(t),onStart:u(e),onMove:n,onEnd:(t,e)=>{delete this.session,r&&i.Gt.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=(0,s.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},39126:(t,e,n)=>{(0,n(12115).createContext)(null)},41049:(t,e)=>{e.qg=function(t,e){let n=new a,i=t.length;if(i<2)return n;let r=e?.decode||h,s=0;do{let e=t.indexOf("=",s);if(-1===e)break;let o=t.indexOf(";",s),a=-1===o?i:o;if(e>a){s=t.lastIndexOf(";",e-1)+1;continue}let h=l(t,s,e),c=u(t,e,h),d=t.slice(h,c);if(void 0===n[d]){let i=l(t,e+1,a),s=u(t,a,i),o=r(t.slice(i,s));n[d]=o}s=a+1}while(s<i);return n},e.lK=function(t,e,a){let l=a?.encode||encodeURIComponent;if(!n.test(t))throw TypeError(`argument name is invalid: ${t}`);let u=l(e);if(!i.test(u))throw TypeError(`argument val is invalid: ${e}`);let h=t+"="+u;if(!a)return h;if(void 0!==a.maxAge){if(!Number.isInteger(a.maxAge))throw TypeError(`option maxAge is invalid: ${a.maxAge}`);h+="; Max-Age="+a.maxAge}if(a.domain){if(!r.test(a.domain))throw TypeError(`option domain is invalid: ${a.domain}`);h+="; Domain="+a.domain}if(a.path){if(!s.test(a.path))throw TypeError(`option path is invalid: ${a.path}`);h+="; Path="+a.path}if(a.expires){var c;if(c=a.expires,"[object Date]"!==o.call(c)||!Number.isFinite(a.expires.valueOf()))throw TypeError(`option expires is invalid: ${a.expires}`);h+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(h+="; HttpOnly"),a.secure&&(h+="; Secure"),a.partitioned&&(h+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():void 0){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${a.priority}`)}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${a.sameSite}`)}return h};let n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,i=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,a=(()=>{let t=function(){};return t.prototype=Object.create(null),t})();function l(t,e,n){do{let n=t.charCodeAt(e);if(32!==n&&9!==n)return e}while(++e<n);return n}function u(t,e,n){for(;e>n;){let n=t.charCodeAt(--e);if(32!==n&&9!==n)return e+1}return n}function h(t){if(-1===t.indexOf("%"))return t;try{return decodeURIComponent(t)}catch(e){return t}}},43050:(t,e,n)=>{n(95155),n(12115),n(90869),n(39126),n(39174),n(80131)},46926:(t,e,n)=>{n.d(e,{P:()=>r});var i=n(31788);function r(t){return t.props[i.n]}},49441:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(43891),r=n(51586),s=n(52290);function o(t,e,n){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===n);let o=s["onTap"+("End"===n?"":n)];o&&i.Gt.postRender(()=>o(e,(0,r.e)(e)))}class a extends s.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.c$)(t,(t,e)=>(o(this.node,e,"Start"),(t,{success:e})=>o(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},49489:(t,e,n)=>{n(21448)},51251:(t,e,n)=>{n(95155),n(12115),n(25214),n(9480)},51442:(t,e,n)=>{n.d(e,{k:()=>i});function i(t,e,n,r={passive:!0}){return t.addEventListener(e,n,r),()=>t.removeEventListener(e,n)}},51508:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},51586:(t,e,n)=>{n.d(e,{F:()=>s,e:()=>r});var i=n(43891);function r(t){return{point:{x:t.pageX,y:t.pageY}}}let s=t=>e=>(0,i.Mc)(e)&&t(e,r(e))},55539:(t,e,n)=>{n(43891),n(21448)},56787:(t,e,n)=>{n(95155),n(12115),n(51508),n(99776),n(82885)},60760:(t,e,n)=>{n(95155);var i=n(12115);n(90869),n(82885),n(97494),n(80845);var r=n(43891);n(51508),i.Component,n(32082)},61665:(t,e,n)=>{n.d(e,{Q:()=>l});var i=n(43891),r=n(21448),s=n(26953),o=n(51586),a=n(2986);class l{constructor(t,e,{transformPagePoint:n,contextWindow:l,dragSnapToOrigin:h=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=c(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=(0,a.w)(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;let{point:r}=t,{timestamp:s}=i.uv;this.history.push({...r,timestamp:s});let{onStart:o,onMove:l}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=u(e,this.transformPagePoint),i.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=c("pointercancel"===t.type?this.lastMoveEventInfo:u(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,s),i&&i(t,s)},!(0,i.Mc)(t))return;this.dragSnapToOrigin=h,this.handlers=e,this.transformPagePoint=n,this.contextWindow=l||window;let d=u((0,o.e)(t),this.transformPagePoint),{point:m}=d,{timestamp:p}=i.uv;this.history=[{...m,timestamp:p}];let{onSessionStart:f}=e;f&&f(t,c(d,this.history)),this.removeListeners=(0,r.Fs)((0,s.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,s.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,s.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,i.WG)(this.updatePoint)}}function u(t,e){return e?{point:e(t.point)}:t}function h(t,e){return{x:t.x-e.x,y:t.y-e.y}}function c({point:t},e){return{point:t,delta:h(t,d(e)),offset:h(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,s=d(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>(0,r.fD)(.1)));)n--;if(!i)return{x:0,y:0};let o=(0,r.Xu)(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};let a={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function d(t){return t[t.length-1]}},66698:(t,e,n)=>{n.d(e,{f:()=>c});var i=n(43891),r=n(21448);let s=t=>null!==t,o={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),l={type:"keyframes",duration:.8},u={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},h=(t,{keyframes:e})=>e.length>2?l:i.fu.has(t)?t.startsWith("scale")?a(e[1]):o:u,c=(t,e,n,o={},a,l)=>u=>{let c=(0,i.rU)(o,t)||{},d=c.delay||o.delay||0,{elapsed:m=0}=o;m-=(0,r.fD)(d);let p={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...c,delay:-m,onUpdate:t=>{e.set(t),c.onUpdate&&c.onUpdate(t)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(c)&&Object.assign(p,h(t,p)),p.duration&&(p.duration=(0,r.fD)(p.duration)),p.repeatDelay&&(p.repeatDelay=(0,r.fD)(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let f=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(f=!0)),(r.W9.instantAnimations||r.W9.skipAnimations)&&(f=!0,p.duration=0,p.delay=0),p.allowFlatten=!c.type&&!c.ease,f&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let r=t.filter(s),o=e&&"loop"!==n&&e%2==1?0:r.length-1;return r[o]}(p.keyframes,c);if(void 0!==t)return void i.Gt.update(()=>{p.onUpdate(t),p.onComplete()})}return c.isSync?new i.sb(p):new i.AT(p)}},70797:(t,e,n)=>{n.d(e,{N:()=>i});let i=(0,n(12115).createContext)({})},71492:(t,e,n)=>{n(82885),n(86811),n(36464)},78660:(t,e,n)=>{n.d(e,{_:()=>a});var i=n(20419),r=n(19578);function s(t,e,n={}){let a=(0,i.K)(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:l=t.getDefaultTransition()||{}}=a||{};n.transitionOverride&&(l=n.transitionOverride);let u=a?()=>Promise.all((0,r.$)(t,a,n)):()=>Promise.resolve(),h=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:u}=l;return function(t,e,n=0,i=0,r=1,a){let l=[],u=(t.variantChildren.size-1)*i,h=1===r?(t=0)=>t*i:(t=0)=>u-t*i;return Array.from(t.variantChildren).sort(o).forEach((t,i)=>{t.notify("AnimationStart",e),l.push(s(t,e,{...a,delay:n+h(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(l)}(t,e,r+i,a,u,n)}:()=>Promise.resolve(),{when:c}=l;if(!c)return Promise.all([u(),h(n.delay)]);{let[t,e]="beforeChildren"===c?[u,h]:[h,u];return t().then(()=>e())}}function o(t,e){return t.sortNodePosition(e)}function a(t,e,n={}){let o;if(t.notify("AnimationStart",e),Array.isArray(e))o=Promise.all(e.map(e=>s(t,e,n)));else if("string"==typeof e)o=s(t,e,n);else{let s="function"==typeof e?(0,i.K)(t,e,n.custom):e;o=Promise.all((0,r.$)(t,s,n))}return o.then(()=>{t.notify("AnimationComplete",e)})}},80845:(t,e,n)=>{n.d(e,{t:()=>i});let i=(0,n(12115).createContext)(null)},88558:(t,e,n)=>{n(12115);var i=n(96488),r=n(81786),s=n(40956);n(82885),n(78660);let o=()=>({});s.B,(0,i.T)({scrapeMotionValuesFromProps:o,createRenderState:o})},90693:(t,e,n)=>{n(12115),n(80845)},90869:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(12115).createContext)({})},93810:(t,e,n)=>{n(12115),n(51442)},98663:(t,e,n)=>{n(82885),n(97494),n(198)},98828:(t,e,n)=>{n(82885),n(86811),n(55539)}}]);