"use strict";exports.id=9618,exports.ids=[9618],exports.modules={29618:(e,t,s)=>{s.d(t,{jinaReranker:()=>a});class r{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/rerank",this.model="jina-reranker-m0",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date(0),errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,s=!1){let r=this.keyUsage.get(e);r&&(r.requests++,r.tokens+=t,r.lastUsed=new Date,s&&(r.errors++,r.lastError=new Date))}async rerankDocuments(e,t,s){if(!t||0===t.length)return[];let r=this.apiKeys.length,a=t.slice(0,2048),i=s||Math.min(a.length,10);for(let t=0;t<r;t++)try{let t=this.getBestKey(),s=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,query:e,documents:a.map(e=>e.content),top_n:i,return_documents:!1})});if(!s.ok){let e=await s.text();throw Error(`HTTP ${s.status}: ${e}`)}let r=await s.json();if(!r.results||!Array.isArray(r.results))throw Error("Invalid response format from Jina Reranker API");this.updateKeyUsage(t,r.usage?.total_tokens||e.length);let n=r.results.map(e=>{let t=a[e.index],s=.3*t.similarity+.7*e.relevance_score;return{content:t.content,document_id:t.document_id,original_similarity:t.similarity,rerank_score:e.relevance_score,final_score:s,metadata:t.metadata}});return n.sort((e,t)=>t.final_score-e.final_score),n.forEach((e,t)=>{}),n}catch(s){let e=this.apiKeys[this.currentKeyIndex-1]||this.apiKeys[this.apiKeys.length-1];if(this.updateKeyUsage(e,0,!0),t===r-1)break}return a.slice(0,i).map(e=>({content:e.content,document_id:e.document_id,original_similarity:e.similarity,rerank_score:e.similarity,final_score:e.similarity,metadata:e.metadata})).sort((e,t)=>t.final_score-e.final_score)}getUsageStats(){let e={};return this.keyUsage.forEach((t,s)=>{let r=this.apiKeys.indexOf(s)+1;e[`key_${r}`]={...t}}),e}resetUsageStats(){this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date(0),errors:0})})}}let a=new r}};