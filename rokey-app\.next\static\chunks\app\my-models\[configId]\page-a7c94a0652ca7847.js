(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{574:(e,t,a)=>{"use strict";a.d(t,{m:()=>s.m});var s=a(76804)},4e3:(e,t,a)=>{"use strict";a.d(t,{J:()=>s.A});var s=a(5196)},31547:(e,t,a)=>{"use strict";a.d(t,{QR:()=>r.A,Uz:()=>l.A,X_:()=>n.A,hc:()=>s.A,kU:()=>i.A});var s=a(1243),r=a(24357),i=a(92657),n=a(78749),l=a(69803)},38152:(e,t,a)=>{"use strict";a.d(t,{Pi:()=>s.A,fK:()=>i.A,uc:()=>r.A});var s=a(55628),r=a(31151),i=a(74500)},39208:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>D});var s=a(95155),r=a(12115),i=a(35695),n=a(75922);let l=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],o=e=>l.find(t=>t.id===e);var d=a(32461),c=a(6865),m=a(89959),u=a(37186),x=a(65529),h=a(67695),g=a(94038),p=a(61316),f=a(85037),b=a(57765),y=a(8246),v=a(31151),j=a(52589),w=a(55424),N=a(80377),k=a(87162),_=a(28003),C=a(79958),A=a(53951),I=a(99323),S=a(49487);let T=n.MG.map(e=>({value:e.id,label:e.name}));function D(){var e,t;let a=(0,i.useParams)().configId,D=(0,k.Z)(),P=(0,I.bu)(),E=(null==P?void 0:P.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:R,isCached:M}=(0,_._)(),{createHoverPrefetch:F}=(0,A.c)(),[O,K]=(0,r.useState)(null),[L,V]=(0,r.useState)(!0),[z,B]=(0,r.useState)(!1),[G,U]=(0,r.useState)((null==(e=T[0])?void 0:e.value)||"openai"),[J,q]=(0,r.useState)(""),[W,H]=(0,r.useState)(""),[X,Q]=(0,r.useState)(""),[Z,Y]=(0,r.useState)(1),[$,ee]=(0,r.useState)(!1),[et,ea]=(0,r.useState)(null),[es,er]=(0,r.useState)(null),[ei,en]=(0,r.useState)(null),[el,eo]=(0,r.useState)(!1),[ed,ec]=(0,r.useState)(null),[em,eu]=(0,r.useState)([]),[ex,eh]=(0,r.useState)(!0),[eg,ep]=(0,r.useState)(null),[ef,eb]=(0,r.useState)(null),[ey,ev]=(0,r.useState)(null),[ej,ew]=(0,r.useState)(null),[eN,ek]=(0,r.useState)(1),[e_,eC]=(0,r.useState)(""),[eA,eI]=(0,r.useState)(!1),[eS,eT]=(0,r.useState)([]),[eD,eP]=(0,r.useState)(!1),[eE,eR]=(0,r.useState)(null),[eM,eF]=(0,r.useState)(!1),[eO,eK]=(0,r.useState)(""),[eL,eV]=(0,r.useState)(""),[ez,eB]=(0,r.useState)(""),[eG,eU]=(0,r.useState)(!1),[eJ,eq]=(0,r.useState)(null),[eW,eH]=(0,r.useState)(null),[eX,eQ]=(0,r.useState)("provider-keys"),eZ=(0,r.useCallback)(async()=>{if(!a)return;let e=R(a);if(e&&e.configDetails){K(e.configDetails),V(!1);return}M(a)||B(!0),V(!0),ea(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations list")}let t=(await e.json()).find(e=>e.id===a);if(!t)throw Error("Configuration not found in the list.");K(t)}catch(e){ea("Error loading model configuration: ".concat(e.message)),K(null)}finally{V(!1),B(!1)}},[a,R,M]);(0,r.useEffect)(()=>{eZ()},[eZ]);let eY=(0,r.useCallback)(async()=>{let e=R(a);if(e&&e.models){en(e.models),eo(!1);return}eo(!0),ec(null),en(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?en(t.models):en([])}catch(e){ec("Error fetching models: ".concat(e.message)),en([])}finally{eo(!1)}},[a,R]);(0,r.useEffect)(()=>{a&&eY()},[a,eY]);let e$=(0,r.useCallback)(async()=>{let e=R(a);if(e&&e.userCustomRoles){eT(e.userCustomRoles),eP(!1);return}eP(!0),eR(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();eT(t)}else{let t;try{t=await e.json()}catch(a){t={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let a=t.error||(t.issues?JSON.stringify(t.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eR(a);else throw Error(a);eT([])}}catch(e){eR(e.message),eT([])}finally{eP(!1)}},[]),e0=(0,r.useCallback)(async()=>{if(!a||!eS)return;let e=R(a);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let t=e.apiKeys.map(async t=>{let a=await fetch("/api/keys/".concat(t.id,"/roles")),s=[];return a.ok&&(s=(await a.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eS.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...t,assigned_roles:s,is_default_general_chat_model:e.defaultChatKeyId===t.id}});eu(await Promise.all(t)),eb(e.defaultChatKeyId),eh(!1);return}eh(!0),ea(e=>e&&e.startsWith("Error loading model configuration:")?e:null),er(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(a));if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch API keys")}let t=await e.json(),s=await fetch("/api/custom-configs/".concat(a,"/default-chat-key"));s.ok;let r=200===s.status?await s.json():null;eb((null==r?void 0:r.id)||null);let i=t.map(async e=>{let t=await fetch("/api/keys/".concat(e.id,"/roles")),a=[];return t.ok&&(a=(await t.json()).map(e=>{let t=o(e.role_name);if(t)return t;let a=eS.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:(null==r?void 0:r.id)===e.id}}),n=await Promise.all(i);eu(n)}catch(e){ea(t=>t?"".concat(t,"; ").concat(e.message):e.message)}finally{eh(!1)}},[a,eS]);(0,r.useEffect)(()=>{O&&e$()},[O,e$]),(0,r.useEffect)(()=>{O&&eS&&e0()},[O,eS,e0]);let e2=(0,r.useMemo)(()=>{if(ei){let e=n.MG.find(e=>e.id===G);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return ei.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[ei,G]),e5=(0,r.useMemo)(()=>{if(ei&&ej){let e=n.MG.find(e=>e.id===ej.provider);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return ei.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[ei,ej]);(0,r.useEffect)(()=>{e2.length>0?q(e2[0].value):q("")},[e2,G]),(0,r.useEffect)(()=>{G&&eY()},[G,eY]);let e1=async e=>{if(e.preventDefault(),!a)return void ea("Configuration ID is missing.");if(em.some(e=>e.predefined_model_id===J))return void ea("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");ee(!0),ea(null),er(null);let t=[...em];try{var s;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:a,provider:G,predefined_model_id:J,api_key_raw:W,label:X,temperature:Z})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to save API key");let r={id:t.id,custom_api_config_id:a,provider:G,predefined_model_id:J,label:X,temperature:Z,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};eu(e=>[...e,r]),er('API key "'.concat(X,'" saved successfully!')),U((null==(s=T[0])?void 0:s.value)||"openai"),H(""),Q(""),Y(1),e2.length>0&&q(e2[0].value)}catch(e){eu(t),ea("Save Key Error: ".concat(e.message))}finally{ee(!1)}},e6=e=>{ew(e),ek(e.temperature||1),eC(e.predefined_model_id)},e4=async()=>{if(!ej)return;if(em.some(e=>e.id!==ej.id&&e.predefined_model_id===e_))return void ea("This model is already configured in this setup. Each model can only be used once per configuration.");eI(!0),ea(null),er(null);let e=[...em];eu(e=>e.map(e=>e.id===ej.id?{...e,temperature:eN,predefined_model_id:e_}:e));try{let t=await fetch("/api/keys?id=".concat(ej.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:eN,predefined_model_id:e_})}),a=await t.json();if(!t.ok)throw eu(e),Error(a.details||a.error||"Failed to update API key");er('API key "'.concat(ej.label,'" updated successfully!')),ew(null)}catch(e){ea("Update Key Error: ".concat(e.message))}finally{eI(!1)}},e3=(e,t)=>{D.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{ep(e),ea(null),er(null);let a=[...em],s=em.find(t=>t.id===e);eu(t=>t.filter(t=>t.id!==e)),(null==s?void 0:s.is_default_general_chat_model)&&eb(null);try{let s=await fetch("/api/keys/".concat(e),{method:"DELETE"}),r=await s.json();if(!s.ok){if(eu(a),eb(ef),404===s.status){eu(t=>t.filter(t=>t.id!==e)),er('API key "'.concat(t,'" was already deleted.'));return}throw Error(r.details||r.error||"Failed to delete API key")}er('API key "'.concat(t,'" deleted successfully!'))}catch(e){throw ea("Delete Key Error: ".concat(e.message)),e}finally{ep(null)}})},e8=async e=>{if(!a)return;ea(null),er(null);let t=[...em];eu(t=>t.map(t=>({...t,is_default_general_chat_model:t.id===e}))),eb(e);try{let s=await fetch("/api/custom-configs/".concat(a,"/default-key-handler/").concat(e),{method:"PUT"}),r=await s.json();if(!s.ok)throw eu(t.map(e=>({...e}))),eb(ef),Error(r.details||r.error||"Failed to set default chat key");er(r.message||"Default general chat key updated!")}catch(e){ea("Set Default Error: ".concat(e.message))}},e9=async(e,t,a)=>{ea(null),er(null);let s="/api/keys/".concat(e.id,"/roles"),r=[...l.map(e=>({...e,isCustom:!1})),...eS.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},i=em.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),n=null;ey&&ey.id===e.id&&(n={...ey,assigned_roles:[...ey.assigned_roles.map(e=>({...e}))]}),eu(s=>s.map(s=>{if(s.id===e.id){let e=a?s.assigned_roles.filter(e=>e.id!==t):[...s.assigned_roles,r];return{...s,assigned_roles:e}}return s})),ey&&ey.id===e.id&&ev(e=>{if(!e)return null;let s=a?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,r];return{...e,assigned_roles:s}});try{let l;l=a?await fetch("".concat(s,"/").concat(t),{method:"DELETE"}):await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let o=await l.json();if(!l.ok){if(eu(i),n)ev(n);else if(ey&&ey.id===e.id){let t=i.find(t=>t.id===e.id);t&&ev(t)}let t=409===l.status&&o.error?o.error:o.details||o.error||(a?"Failed to unassign role":"Failed to assign role");throw Error(t)}er(o.message||"Role '".concat(r.name,"' ").concat(a?"unassigned":"assigned"," successfully."))}catch(e){ea("Role Update Error: ".concat(e.message))}},e7=async()=>{if(!eO.trim()||eO.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eO.trim()))return void eq("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(l.some(e=>e.id.toLowerCase()===eO.trim().toLowerCase())||eS.some(e=>e.role_id.toLowerCase()===eO.trim().toLowerCase()))return void eq("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eL.trim())return void eq("Role Name is required.");eq(null),eU(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eO.trim(),name:eL.trim(),description:ez.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(s){let a=await e.text().catch(()=>"HTTP status ".concat(e.status));t={error:"Server error, could not parse response.",details:a}}let a=t.error||"Failed to create custom role.";if(t.details)a+=" (Details: ".concat(t.details,")");else if(t.issues){let e=Object.entries(t.issues).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a.join(", "))}).join("; ");a+=" (Issues: ".concat(e,")")}throw Error(a)}let t=await e.json();eK(""),eV(""),eB(""),e$(),er("Custom role '".concat(t.name,"' created successfully! It is now available globally."))}catch(e){eq(e.message)}finally{eU(!1)}},te=(e,t)=>{e&&D.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(t,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eH(e),eR(null),eq(null),er(null);try{let s=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),r=await s.json();if(!s.ok)throw Error(r.error||"Failed to delete custom role");eT(t=>t.filter(t=>t.id!==e)),er(r.message||'Global custom role "'.concat(t,'" deleted successfully.')),a&&e0()}catch(e){throw eR("Error deleting role: ".concat(e.message)),e}finally{eH(null)}})};return z&&!M(a)?(0,s.jsx)(C.A,{}):L&&!O?(0,s.jsx)(C._,{}):(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("button",{onClick:()=>E("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,s.jsx)("div",{className:"flex-1",children:O?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,s.jsx)(u.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:O.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[(0,s.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",O.id]})]}):et&&!L?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:(0,s.jsx)(j.A,{className:"h-6 w-6 text-red-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),(0,s.jsx)("p",{className:"text-red-500 mt-1",children:et.replace("Error loading model configuration: ","")})]})]}):(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),(0,s.jsx)("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),O&&(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,s.jsxs)("button",{onClick:()=>E("/routing-setup/".concat(a,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...F(a),children:[(0,s.jsx)(u.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),es&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("p",{className:"text-green-800 font-medium",children:es})]})}),et&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("p",{className:"text-red-800 font-medium",children:et})]})})]}),O&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-2",children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("button",{onClick:()=>eQ("provider-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ".concat("provider-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Provider API Keys"})]})}),(0,s.jsx)("button",{onClick:()=>eQ("user-api-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ".concat("user-api-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Generated API Keys"})]})})]})}),"provider-keys"===eX&&(0,s.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,s.jsx)("div",{className:"xl:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,s.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add Provider API Key"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Configure new provider key"})]})]}),(0,s.jsxs)("form",{onSubmit:e1,className:"space-y-5",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,s.jsx)("select",{id:"provider",value:G,onChange:e=>{U(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:T.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),(0,s.jsx)("input",{id:"apiKeyRaw",type:"password",value:W,onChange:e=>H(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),el&&null===ei&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),ed&&(0,s.jsx)("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:ed})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),(0,s.jsx)("select",{id:"predefinedModelId",value:J,onChange:e=>q(e.target.value),disabled:!e2.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:e2.length>0?e2.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):(0,s.jsx)("option",{value:"",disabled:!0,children:null===ei&&el?"Loading models...":"Select a provider first"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),(0,s.jsx)("input",{type:"text",id:"label",value:X,onChange:e=>Q(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",(0,s.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:Z,onChange:e=>Y(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:Z,onChange:e=>Y(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,s.jsx)("button",{type:"submit",disabled:$||!J||""===J||!W.trim()||!X.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:$?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),(0,s.jsxs)("div",{className:"text-xs text-blue-800 space-y-1",children:[(0,s.jsxs)("p",{children:["✅ ",(0,s.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,s.jsxs)("p",{children:["✅ ",(0,s.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,s.jsxs)("p",{children:["❌ ",(0,s.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,s.jsx)("div",{className:"xl:col-span-3",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,s.jsx)(g.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),ex&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!ex&&0===em.length&&(!et||et&&et.startsWith("Error loading model configuration:"))&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,s.jsx)(g.A,{className:"h-6 w-6 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!ex&&em.length>0&&(0,s.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:em.map((e,t)=>(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[(0,s.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),(0,s.jsxs)("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,s.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):(0,s.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_general_chat_model&&(0,s.jsx)("button",{onClick:()=>e8(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,s.jsx)("button",{onClick:()=>e6(e),disabled:eg===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>ev(e),disabled:eg===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>e3(e.id,e.label),disabled:eg===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:eg===e.id?(0,s.jsx)(v.A,{className:"h-4 w-4 animate-pulse"}):(0,s.jsx)(v.A,{className:"h-4 w-4"})})]})]})},e.id))}),!ex&&et&&!et.startsWith("Error loading model configuration:")&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(j.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",et]})]})})]})})]}),"user-api-keys"===eX&&(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:(0,s.jsx)(S.z,{configId:a,configName:O.name})})]}),ey&&(()=>{if(!ey)return null;let e=[...l.map(e=>({...e,isCustom:!1})),...eS.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",(0,s.jsx)("span",{className:"text-orange-600",children:ey.label})]}),(0,s.jsx)("button",{onClick:()=>{ev(null),eF(!1),eq(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,s.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eE&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,s.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eE]})}),(0,s.jsx)("div",{className:"flex justify-end mb-4",children:(0,s.jsxs)("button",{onClick:()=>eF(!eM),className:"btn-primary text-sm inline-flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),eM?"Cancel New Role":"Create New Custom Role"]})}),eM&&(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,s.jsx)("input",{type:"text",id:"newCustomRoleId",value:eO,onChange:e=>eK(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),(0,s.jsx)("input",{type:"text",id:"newCustomRoleName",value:eL,onChange:e=>eV(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (optional, max 500 chars)"}),(0,s.jsx)("textarea",{id:"newCustomRoleDescription",value:ez,onChange:e=>eB(e.target.value),rows:2,className:"form-input",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eJ&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-800 text-sm",children:eJ})}),(0,s.jsx)("button",{onClick:e7,disabled:eG,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eG?"Saving Role...":"Save Custom Role"})]})]})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),(0,s.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eD&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=ey.assigned_roles.some(t=>t.id===e.id);return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(t?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"),children:[(0,s.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,s.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:t,onChange:()=>e9(ey,e.id,t),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),(0,s.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(t?"text-orange-800":"text-gray-900"),children:e.name}),e.isCustom&&(0,s.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,s.jsx)("button",{onClick:()=>te(e.databaseId,e.name),disabled:eW===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eW===e.databaseId?(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(v.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>{ev(null),eF(!1),eq(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ej&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"card w-full max-w-lg",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),(0,s.jsx)("button",{onClick:()=>ew(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,s.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:ej.label}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Current: ",ej.provider," (",ej.predefined_model_id,")"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,s.jsx)("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:(null==(t=n.MG.find(e=>e.id===ej.provider))?void 0:t.name)||ej.provider}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),(0,s.jsx)("select",{id:"editModelId",value:e_,onChange:e=>eC(e.target.value),disabled:!e5.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:e5.length>0?e5.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)):(0,s.jsx)("option",{value:"",disabled:!0,children:el?"Loading models...":"No models available"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",eN]}),(0,s.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:eN,onChange:e=>ek(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,s.jsx)("span",{children:"0.0 (Focused)"}),(0,s.jsx)("span",{children:"1.0 (Balanced)"}),(0,s.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{onClick:()=>ew(null),className:"btn-secondary",disabled:eA,children:"Cancel"}),(0,s.jsx)("button",{onClick:e4,disabled:eA,className:"btn-primary",children:eA?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!O&&!L&&!et&&(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)(h.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,s.jsxs)("button",{onClick:()=>E("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,s.jsx)(N.A,{isOpen:D.isOpen,onClose:D.hideConfirmation,onConfirm:D.onConfirm,title:D.title,message:D.message,confirmText:D.confirmText,cancelText:D.cancelText,type:D.type,isLoading:D.isLoading}),(0,s.jsx)(w.m_,{id:"global-tooltip"})]})}},44469:(e,t,a)=>{Promise.resolve().then(a.bind(a,39208))},75922:(e,t,a)=>{"use strict";a.d(t,{MG:()=>s});let s=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76288:(e,t,a)=>{"use strict";a.d(t,{X:()=>s.A});var s=a(54416)},78507:(e,t,a)=>{"use strict";a.d(t,{FW:()=>r.A,X:()=>l.A,ek:()=>n.A,qz:()=>s.A,wB:()=>i.A});var s=a(34869),r=a(84616),i=a(381),n=a(75525),l=a(54416)},84168:(e,t,a)=>{"use strict";a.d(t,{FW:()=>i.A,RI:()=>s.A,Uz:()=>r.A,e9:()=>n.A});var s=a(85339),r=a(69803),i=a(84616),n=a(53904)},86558:(e,t,a)=>{"use strict";a.d(t,{Il:()=>s.A,RI:()=>r.A,Vv:()=>i.A,e9:()=>l.A,f5:()=>n.A});var s=a(79397),r=a(85339),i=a(69074),n=a(91788),l=a(53904)},93295:(e,t,a)=>{"use strict";a.d(t,{Il:()=>s.A,QR:()=>i.A,TB:()=>d.A,Vv:()=>r.A,ek:()=>o.A,qz:()=>n.A,wB:()=>l.A});var s=a(79397),r=a(69074),i=a(24357),n=a(34869),l=a(381),o=a(75525),d=a(62525)},99323:(e,t,a)=>{"use strict";a.d(t,{bu:()=>o,i9:()=>l});var s=a(95155),r=a(12115),i=a(35695);let n=(0,r.createContext)(void 0);function l(e){let{children:t}=e,[a,l]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),[c,m]=(0,r.useState)([]),[u,x]=(0,r.useState)(new Set),[h,g]=(0,r.useState)(!1),p=(0,i.usePathname)(),f=(0,i.useRouter)(),b=(0,r.useRef)(null),y=(0,r.useRef)([]),v=(0,r.useRef)(null),j=(0,r.useRef)(0),w=(0,r.useRef)({}),N=(0,r.useRef)({});(0,r.useEffect)(()=>{g(!0)},[]);let k=(0,r.useCallback)(e=>{},[h]);(0,r.useEffect)(()=>{p&&!c.includes(p)&&(m(e=>[...e,p]),x(e=>new Set([...e,p])))},[p,c]),(0,r.useEffect)(()=>{k("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(o,", current=").concat(p,", navigationId=").concat(v.current)),o&&v.current&&p===o&&(k("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(o," -> ").concat(p)),b.current&&(clearTimeout(b.current),b.current=null),l(!1),d(null),v.current=null,y.current=y.current.filter(e=>e.route!==o))},[p,o,k]),(0,r.useEffect)(()=>{a&&o&&p===o&&(k("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),l(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},[p,o,a,k]);let _=(0,r.useCallback)(e=>u.has(e),[u]),C=(0,r.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:a}=e;k("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(a,")")),b.current&&(clearTimeout(b.current),b.current=null),v.current=a;let s=_(t);s&&(k("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{v.current===a&&l(!1)},100));try{f.push(t)}catch(e){k("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}b.current=setTimeout(()=>{if(k("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(a,"), current path: ").concat(p)),v.current===a){k("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){k("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}l(!1),d(null),v.current=null}b.current=null},s?800:3e3)},[f,p,_,k]),A=(0,r.useCallback)(e=>{if(p===e||!h)return;let t=Date.now();if(t-j.current<100&&o===e)return void k("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(j.current=t,w.current[e]||(w.current[e]=0),w.current[e]++,N.current[e]&&clearTimeout(N.current[e]),N.current[e]=setTimeout(()=>{w.current[e]=0},2e3),w.current[e]>=3){k("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),w.current[e]=0,window.location.href=e;return}b.current&&(clearTimeout(b.current),b.current=null),l(!0),d(e);let a="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));y.current=[{route:e,timestamp:t,id:a}],C()},[p,o,C,k,h]),I=(0,r.useCallback)(()=>{b.current&&(clearTimeout(b.current),b.current=null),l(!1),d(null),v.current=null,y.current=[]},[]);return(0,r.useEffect)(()=>{if(!h)return;let e=()=>{!document.hidden&&a&&(k("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{o&&p===o&&(k("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),l(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[a,o,p,k,h]),(0,r.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]),(0,s.jsx)(n.Provider,{value:{isNavigating:a,targetRoute:o,navigateOptimistically:A,clearNavigation:I,isPageCached:_,navigationHistory:c},children:t})}function o(){return(0,r.useContext)(n)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[7874,5738,9968,6060,6308,563,2662,8669,8848,622,2432,408,2975,2336,5144,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(44469)),_N_E=e.O()}]);