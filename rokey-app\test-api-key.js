#!/usr/bin/env node

/**
 * RouKey API Key Test Script
 * Tests the user-generated API key functionality with various scenarios
 */

const API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6';
const BASE_URL = 'https://roukey.online'; // Production deployment URL
const ENDPOINT = `${BASE_URL}/api/external/v1/chat/completions`;

// Test scenarios
const testCases = [
  {
    name: 'Simple greeting',
    messages: [{ role: 'user', content: 'hi' }],
    stream: false
  },
  {
    name: 'Code generation request',
    messages: [{ role: 'user', content: 'code a snake game in python' }],
    stream: false
  },
  {
    name: 'Complex creative task',
    messages: [{ role: 'user', content: 'brainstorm an idea for a book and write a python code about it' }],
    stream: false
  },
  {
    name: 'Simple greeting (streaming)',
    messages: [{ role: 'user', content: 'hi' }],
    stream: true
  },
  {
    name: 'Code generation (streaming)',
    messages: [{ role: 'user', content: 'code a simple calculator in python' }],
    stream: true
  },
  {
    name: 'Role-based routing test',
    messages: [{ role: 'user', content: 'Explain quantum computing in simple terms' }],
    role: 'science_expert',
    stream: false
  },
  {
    name: 'Temperature test (creative)',
    messages: [{ role: 'user', content: 'Write a creative story about a robot' }],
    temperature: 1.5,
    stream: false
  },
  {
    name: 'Temperature test (factual)',
    messages: [{ role: 'user', content: 'What is the capital of France?' }],
    temperature: 0.1,
    stream: false
  },
  {
    name: 'Production API health check',
    messages: [{ role: 'user', content: 'Hello, this is a test of the production API' }],
    stream: false
  },
  {
    name: 'Multi-turn conversation test',
    messages: [
      { role: 'user', content: 'What is machine learning?' },
      { role: 'assistant', content: 'Machine learning is a subset of artificial intelligence...' },
      { role: 'user', content: 'Can you give me a simple example?' }
    ],
    stream: false
  },
  {
    name: 'Long content generation (streaming)',
    messages: [{ role: 'user', content: 'Write a detailed explanation of how neural networks work, including examples and applications' }],
    stream: true
  }
];

async function makeRequest(testCase, testNumber) {
  console.log(`\n🧪 Test ${testNumber}: ${testCase.name}`);
  console.log('=' .repeat(50));
  
  const requestBody = {
    // No model specified - RouKey will use your configured models
    messages: testCase.messages,
    stream: testCase.stream || false,
    ...(testCase.temperature && { temperature: testCase.temperature }),
    ...(testCase.role && { role: testCase.role }),
    max_tokens: 500
  };

  console.log('📤 Request:', JSON.stringify(requestBody, null, 2));
  console.log('🔑 Using API Key:', `${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);
  console.log('📡 X-API-Key Header:', `${API_KEY.substring(0, 20)}...${API_KEY.slice(-4)}`);

  try {
    const startTime = Date.now();

    const response = await fetch(ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Test-Script/1.0'
      },
      body: JSON.stringify(requestBody)
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Duration: ${duration}ms`);

    // Show important headers for production debugging
    const importantHeaders = ['content-type', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-request-id', 'server'];
    const headers = Object.fromEntries(response.headers.entries());
    const filteredHeaders = Object.fromEntries(
      Object.entries(headers).filter(([key]) => importantHeaders.includes(key.toLowerCase()))
    );
    console.log('📋 Important Headers:', filteredHeaders);

    if (testCase.stream) {
      console.log('📡 Streaming response:');
      console.log('─'.repeat(30));
      
      if (response.body) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            process.stdout.write(chunk);
            fullResponse += chunk;
          }
        } catch (streamError) {
          console.error('❌ Streaming error:', streamError.message);
        }
        
        console.log('\n─'.repeat(30));
        console.log(`📏 Total response length: ${fullResponse.length} characters`);
      } else {
        console.log('❌ No response body for streaming');
      }
    } else {
      const responseText = await response.text();
      let responseData;

      try {
        responseData = JSON.parse(responseText);
        console.log('📥 Response:', JSON.stringify(responseData, null, 2));

        if (responseData.choices && responseData.choices[0]) {
          const content = responseData.choices[0].message?.content || '';
          console.log(`📏 Response length: ${content.length} characters`);
        }
      } catch (parseError) {
        console.log('📥 Raw Response (not JSON):', responseText);
      }
    }

    if (!response.ok) {
      console.log('❌ Request failed');
      return false;
    }

    console.log('✅ Request successful');
    return true;

  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

async function testConnectivity() {
  console.log('🔍 Testing connectivity to production deployment...');

  try {
    const response = await fetch(`${BASE_URL}/api/system-status`, {
      method: 'GET',
      headers: {
        'User-Agent': 'RouKey-Test-Script/1.0'
      }
    });

    console.log(`📊 System Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      await response.text(); // Consume response body
      console.log('✅ Production deployment is accessible');
      return true;
    } else {
      console.log('⚠️  Production deployment returned non-200 status');
      return false;
    }
  } catch (error) {
    console.error('❌ Cannot reach production deployment:', error.message);
    return false;
  }
}

async function debugApiKey() {
  console.log('🔍 Debugging API key...');
  console.log(`🔑 Full API Key: ${API_KEY}`);
  console.log(`📏 Key Length: ${API_KEY.length}`);
  console.log(`🏷️  Key Format: ${API_KEY.startsWith('rk_live_') ? 'Valid prefix' : 'Invalid prefix'}`);

  // Test with debug endpoint first
  try {
    console.log('🧪 Testing debug auth endpoint...');
    const debugResponse = await fetch(`${BASE_URL}/api/debug/auth-test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Debug/1.0'
      },
      body: JSON.stringify({ test: true })
    });

    console.log(`📊 Debug Auth Status: ${debugResponse.status}`);
    const debugData = await debugResponse.json();
    console.log(`📥 Debug Auth Response:`, JSON.stringify(debugData, null, 2));

  } catch (error) {
    console.error('❌ Debug auth request failed:', error.message);
  }

  // Test with a simple request to see the exact error
  try {
    console.log('🧪 Testing main endpoint...');
    const response = await fetch(`${BASE_URL}/api/external/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Debug/1.0'
      },
      body: JSON.stringify({
        messages: [{ role: 'user', content: 'debug test' }],
        max_tokens: 10
      })
    });

    console.log(`📊 Main Endpoint Status: ${response.status}`);
    const responseText = await response.text();
    console.log(`📥 Main Endpoint Response:`, responseText);

  } catch (error) {
    console.error('❌ Main endpoint request failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting RouKey API Key Tests');
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...`);
  console.log(`🌐 Endpoint: ${ENDPOINT}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);

  // Test connectivity first
  const isConnected = await testConnectivity();
  if (!isConnected) {
    console.log('❌ Cannot proceed with tests - deployment not accessible');
    return;
  }

  console.log('\n');
  await debugApiKey();

  console.log('\n⏳ Waiting 2 seconds before starting API key tests...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  let successCount = 0;
  let totalTests = testCases.length;

  for (let i = 0; i < testCases.length; i++) {
    const success = await makeRequest(testCases[i], i + 1);
    if (success) successCount++;
    
    // Wait between requests to avoid rate limiting
    if (i < testCases.length - 1) {
      console.log('\n⏳ Waiting 2 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Successful: ${successCount}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - successCount}/${totalTests}`);
  console.log(`📈 Success Rate: ${((successCount / totalTests) * 100).toFixed(1)}%`);
  console.log(`📅 Completed at: ${new Date().toISOString()}`);
  
  if (successCount === totalTests) {
    console.log('🎉 All tests passed! Your API key is working perfectly.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
runTests().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
