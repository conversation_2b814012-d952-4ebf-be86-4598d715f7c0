(()=>{var e={};e.id=6782,e.ids=[6782],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},25282:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>c});var i=t(96559),n=t(48088),o=t(37719),u=t(32190);let a=(0,t(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("userId");if(!t)return u.NextResponse.json({error:"Missing userId parameter"},{status:400});let{data:s,error:i}=await a.from("subscriptions").select("*").eq("user_id",t).order("created_at",{ascending:!1}).limit(1).single(),{data:n,error:o}=await a.from("user_profiles").select("subscription_tier").eq("id",t).single();if(o)return u.NextResponse.json({error:"User profile not found"},{status:404});if(i||!s)return u.NextResponse.json({hasActiveSubscription:!1,tier:n.subscription_tier||"starter",status:null,currentPeriodEnd:null,cancelAtPeriodEnd:!1});let p="active"===s.status,c=new Date(s.current_period_end)<new Date;return u.NextResponse.json({hasActiveSubscription:p&&!c,tier:s.tier,status:s.status,currentPeriodEnd:s.current_period_end,currentPeriodStart:s.current_period_start,cancelAtPeriodEnd:s.cancel_at_period_end,stripeCustomerId:s.stripe_customer_id,stripeSubscriptionId:s.stripe_subscription_id})}catch(e){return u.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{let{userId:r}=await e.json();if(!r)return u.NextResponse.json({error:"Missing userId"},{status:400});let t=new Date().toISOString().slice(0,7),{data:s,error:i}=await a.from("usage_tracking").select("*").eq("user_id",r).eq("month_year",t).single(),{data:n}=await a.from("user_profiles").select("subscription_tier").eq("id",r).single(),o=n?.subscription_tier||"starter",{count:p}=await a.from("custom_api_configs").select("*",{count:"exact",head:!0}).eq("user_id",r),{count:c}=await a.from("api_keys").select("*",{count:"exact",head:!0}).eq("user_id",r),d=function(e){switch(e){case"starter":default:return{configurations:5,apiKeysPerConfig:25,apiRequests:999999};case"professional":return{configurations:25,apiKeysPerConfig:100,apiRequests:999999};case"enterprise":return{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999}}}(o);return u.NextResponse.json({tier:o,usage:{configurations:p||0,apiKeys:c||0,apiRequests:s?.api_requests_count||0},limits:d,canCreateConfig:(p||0)<d.configurations,canCreateApiKey:(c||0)<d.apiKeysPerConfig})}catch(e){return u.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe/subscription-status/route",pathname:"/api/stripe/subscription-status",filename:"route",bundlePath:"app/api/stripe/subscription-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\subscription-status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:f}=d;function _(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398],()=>t(25282));module.exports=s})();