{"/api/activity/route": "/api/activity", "/api/admin/cleanup-semantic-cache/route": "/api/admin/cleanup-semantic-cache", "/api/admin/populate-cost-tiers/route": "/api/admin/populate-cost-tiers", "/api/analytics/summary/route": "/api/analytics/summary", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/conversations/route": "/api/chat/conversations", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/chat/messages/route": "/api/chat/messages", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/custom-configs/route": "/api/custom-configs", "/api/debug/auth-test/route": "/api/debug/auth-test", "/api/debug/checkout/route": "/api/debug/checkout", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/documents/list/route": "/api/documents/list", "/api/documents/search/route": "/api/documents/search", "/api/documents/upload/route": "/api/documents/upload", "/api/external/v1/async/result/[jobId]/route": "/api/external/v1/async/result/[jobId]", "/api/external/v1/async/status/[jobId]/route": "/api/external/v1/async/status/[jobId]", "/api/external/v1/async/submit/route": "/api/external/v1/async/submit", "/api/external/v1/chat/completions/route": "/api/external/v1/chat/completions", "/api/internal/classify-multi-role/route": "/api/internal/classify-multi-role", "/api/internal/async/process/route": "/api/internal/async/process", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/keys/route": "/api/keys", "/api/logs/route": "/api/logs", "/api/orchestration/classify-roles/route": "/api/orchestration/classify-roles", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/playground/route": "/api/playground", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/providers/list-models/route": "/api/providers/list-models", "/api/quality-analytics/route": "/api/quality-analytics", "/api/quality-feedback/route": "/api/quality-feedback", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/system-status/route": "/api/system-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/test/semantic-cache/route": "/api/test/semantic-cache", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/training/jobs/route": "/api/training/jobs", "/api/user-api-keys/route": "/api/user-api-keys", "/api/user-api-keys/[keyId]/route": "/api/user-api-keys/[keyId]", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/api/user/subscription-tier/route": "/api/user/subscription-tier", "/auth/callback/route": "/auth/callback", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/add-keys/page": "/add-keys", "/analytics/page": "/analytics", "/auth/signup/page": "/auth/signup", "/auth/signin/page": "/auth/signin", "/checkout/page": "/checkout", "/auth/verify-email/page": "/auth/verify-email", "/debug-session/page": "/debug-session", "/dashboard/page": "/dashboard", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/my-models/page": "/my-models", "/page": "/", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/pricing/page": "/pricing", "/routing-setup/page": "/routing-setup", "/playground/page": "/playground", "/training/page": "/training", "/about/page": "/about", "/contact/page": "/contact", "/features/page": "/features", "/routing-strategies/page": "/routing-strategies"}