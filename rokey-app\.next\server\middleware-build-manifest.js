globalThis.__BUILD_MANIFEST={polyfillFiles:["static/chunks/polyfills-42372ed130431b0a.js"],devFiles:[],ampDevFiles:[],lowPriorityFiles:[],rootMainFiles:["static/chunks/webpack-94c71b8c1b30588f.js","static/chunks/6642-c5138f32c08e486c.js","static/chunks/7706-8f8e19de8738c6d0.js","static/chunks/7544-188475e6877d1aca.js","static/chunks/2138-e0f29e8345bcb260.js","static/chunks/4518-5634dec63e57ae98.js","static/chunks/9248-c043180165717eeb.js","static/chunks/2324-6ce6b9cacc07163d.js","static/chunks/main-app-7603288af596d45a.js"],rootMainFilesTree:{},pages:{"/_app":["static/chunks/webpack-94c71b8c1b30588f.js","static/chunks/react-b6eb68949bf517f4.js","static/chunks/1630-da514b75321b86a2.js","static/chunks/7701-79366fc10909eb39.js","static/chunks/main-754975ec0b97c024.js","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","static/chunks/vendors-2ced652b-85aee2751f317897.js","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","static/chunks/pages/_app-0beab4e43583bc3c.js"],"/_error":["static/chunks/webpack-94c71b8c1b30588f.js","static/chunks/react-b6eb68949bf517f4.js","static/chunks/1630-da514b75321b86a2.js","static/chunks/7701-79366fc10909eb39.js","static/chunks/main-754975ec0b97c024.js","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","static/chunks/vendors-2ced652b-85aee2751f317897.js","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","static/chunks/pages/_error-ccf8073a28f1e494.js"]},ampFirstPages:[]},globalThis.__BUILD_MANIFEST.lowPriorityFiles=["/static/"+process.env.__NEXT_BUILD_ID+"/_buildManifest.js",,"/static/"+process.env.__NEXT_BUILD_ID+"/_ssgManifest.js"];