(()=>{var e={};e.id=9679,e.ids=[1489,9618,9679],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>i});var r=s(34386),a=s(44999);async function i(){let e=await (0,a.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function n(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29618:(e,t,s)=>{"use strict";s.d(t,{jinaReranker:()=>a});class r{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/rerank",this.model="jina-reranker-m0",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date(0),errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,s=!1){let r=this.keyUsage.get(e);r&&(r.requests++,r.tokens+=t,r.lastUsed=new Date,s&&(r.errors++,r.lastError=new Date))}async rerankDocuments(e,t,s){if(!t||0===t.length)return[];let r=this.apiKeys.length,a=t.slice(0,2048),i=s||Math.min(a.length,10);for(let t=0;t<r;t++)try{let t=this.getBestKey(),s=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,query:e,documents:a.map(e=>e.content),top_n:i,return_documents:!1})});if(!s.ok){let e=await s.text();throw Error(`HTTP ${s.status}: ${e}`)}let r=await s.json();if(!r.results||!Array.isArray(r.results))throw Error("Invalid response format from Jina Reranker API");this.updateKeyUsage(t,r.usage?.total_tokens||e.length);let n=r.results.map(e=>{let t=a[e.index],s=.3*t.similarity+.7*e.relevance_score;return{content:t.content,document_id:t.document_id,original_similarity:t.similarity,rerank_score:e.relevance_score,final_score:s,metadata:t.metadata}});return n.sort((e,t)=>t.final_score-e.final_score),n.forEach((e,t)=>{}),n}catch(s){let e=this.apiKeys[this.currentKeyIndex-1]||this.apiKeys[this.apiKeys.length-1];if(this.updateKeyUsage(e,0,!0),t===r-1)break}return a.slice(0,i).map(e=>({content:e.content,document_id:e.document_id,original_similarity:e.similarity,rerank_score:e.similarity,final_score:e.similarity,metadata:e.metadata})).sort((e,t)=>t.final_score-e.final_score)}getUsageStats(){let e={};return this.keyUsage.forEach((t,s)=>{let r=this.apiKeys.indexOf(s)+1;e[`key_${r}`]={...t}}),e}resetUsageStats(){this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date(0),errors:0})})}}let a=new r},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49034:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var r={};s.r(r),s.d(r,{POST:()=>d});var a=s(96559),i=s(48088),n=s(37719),o=s(32190),u=s(2507),c=s(88108),l=s(29618);async function d(e){try{let t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{query:a,configId:i,limit:n=8,threshold:d=.5,useReranker:p=!0}=await e.json();if(!a||!i)return o.NextResponse.json({error:"Query and configId are required"},{status:400});let h=await c.jinaEmbeddings.embedQuery(a),y=p?Math.min(3*n,50):n,{data:m,error:_}=await t.rpc("search_document_chunks",{query_embedding:h,config_id:i,user_id_param:s.id,match_threshold:d,match_count:y});if(_)return o.NextResponse.json({error:"Search failed",details:_.message},{status:500});if(!m||0===m.length)return o.NextResponse.json({success:!0,results:[],query:a,total_results:0});let g=m;if(p&&m.length>1)try{let e=m.map(e=>({content:e.content,document_id:e.document_id,similarity:e.similarity,metadata:e.metadata}));g=(await l.jinaReranker.rerankDocuments(a,e,n)).map(e=>({document_id:e.document_id,content:e.content,similarity:e.original_similarity,rerank_score:e.rerank_score,final_score:e.final_score,metadata:e.metadata}))}catch(e){g=m.slice(0,n)}else g=m.slice(0,n);if(g&&g.length>0){let e=[...new Set(g.map(e=>e.document_id))],{data:s}=await t.from("documents").select("id, filename, file_type").in("id",e),r=g.map(e=>{let t=s?.find(t=>t.id===e.document_id);return{...e,document:t||null}});return o.NextResponse.json({success:!0,results:r,query:a,total_results:g.length,reranked:p&&m.length>1})}return o.NextResponse.json({success:!0,results:[],query:a,total_results:0})}catch(e){return o.NextResponse.json({error:"Search failed",details:e.message},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/documents/search/route",pathname:"/api/documents/search",filename:"route",bundlePath:"app/api/documents/search/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\documents\\search\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:m}=p;function _(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88108:(e,t,s)=>{"use strict";s.d(t,{jinaEmbeddings:()=>a});class r{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,s=!1){let r=this.keyUsage.get(e);r&&(r.requests++,r.tokens+=t,r.lastUsed=new Date,s&&(r.errors++,r.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,s=null;for(let r=0;r<t;r++)try{let t=this.getBestKey(),s=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!s.ok){let e=await s.text();if(429===s.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${s.status}: ${e}`)}let r=await s.json();if(!r.data||0===r.data.length)throw Error("No embedding data returned from Jina API");let a=r.data[0].embedding;return this.updateKeyUsage(t,r.usage?.total_tokens||e.length),a}catch(e){if(s=e,r===t-1)break}throw Error(`All Jina API keys failed. Last error: ${s?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let s=0;s<e.length;s++){let r=await this.embedQuery(e[s]);t.push(r),s<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,s)=>{let r=this.keyUsage.get(t);r&&(e[`key_${s+1}`]={...r})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let a=new r},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>s(49034));module.exports=r})();