(()=>{var e={};e.id=5690,e.ids=[5690],e.modules={192:(e,t,s)=>{Promise.resolve().then(s.bind(s,39482))},2969:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13344:(e,t,s)=>{Promise.resolve().then(s.bind(s,69188))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39482:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(71031),l=s(97450),d=s(2969),c=s(71178),m=s(26403),p=s(50181),x=s(20404),u=s(9776),h=s(5097);function f(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[f,g]=(0,a.useState)(null),[y,b]=(0,a.useState)(""),[j,v]=(0,a.useState)(!1),w=(0,x.Z)(),[N,C]=(0,a.useState)(!1),{createHoverPrefetch:A,prefetchManageKeysData:k}=(0,h._)(),P=async()=>{i(!0),g(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let s=await e.json();t(s)}catch(e){g(e.message)}finally{i(!1)}},q=async e=>{if(e.preventDefault(),!y.trim())return void g("Configuration name cannot be empty.");v(!0),g(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:y})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to create configuration");b(""),C(!1),await P()}catch(e){g(e.message)}finally{v(!1)}},M=(e,t)=>{w.showConfirmation({title:"Delete Configuration",message:`Are you sure you want to delete "${t}"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.`,confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{g(null);try{let t=await fetch(`/api/custom-configs/${e}`,{method:"DELETE"}),s=await t.json();if(!t.ok)throw Error(s.details||s.error||"Failed to delete configuration");await P()}catch(e){throw g(`Failed to delete: ${e.message}`),e}})};return(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,r.jsxs)("button",{onClick:()=>C(!N),className:N?"btn-secondary":"btn-primary",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),N?"Cancel":"Create New Model"]})]}),f&&(0,r.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,r.jsx)("p",{className:"text-red-800",children:f})]})}),N&&(0,r.jsxs)("div",{className:"card max-w-md animate-scale-in p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),(0,r.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),(0,r.jsx)("input",{type:"text",id:"configName",value:y,onChange:e=>b(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"btn-primary w-full",children:j?"Creating...":"Create Model"})]})]}),s&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,r.jsx)(u.B0,{},t))}),!s&&!e.length&&!f&&!N&&(0,r.jsx)("div",{className:"card text-center py-12",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,r.jsx)(l.A,{className:"h-8 w-8 text-orange-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,r.jsxs)("button",{onClick:()=>C(!0),className:"btn-primary",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})]})}),!s&&e.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:`${100*t}ms`},children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,r.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,r.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:(0,r.jsx)(l.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,r.jsx)(n(),{href:`/my-models/${e.id}`,className:"flex-1",...A(e.id),children:(0,r.jsxs)("button",{className:"btn-primary w-full",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,r.jsxs)("button",{onClick:()=>M(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,r.jsx)(p.A,{isOpen:w.isOpen,onClose:w.hideConfirmation,onConfirm:w.onConfirm,title:w.title,message:w.message,confirmText:w.confirmText,cancelText:w.cancelText,type:w.type,isLoading:w.isLoading})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69188:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89507:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["my-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69188)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-models/page",pathname:"/my-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7482,4912,8834],()=>s(89507));module.exports=r})();