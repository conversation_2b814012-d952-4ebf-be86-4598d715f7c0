"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6060],{8413:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(12115),l=a(57514);let i=new Map;function n(e){let{configId:s,onRetry:a,className:n="",disabled:c=!1}=e,[d,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)([]),[u,h]=(0,r.useState)(!1),[g,p]=(0,r.useState)(!1),y=(0,r.useRef)(null),j=(0,r.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=i.get(s);if(e&&Date.now()-e.timestamp<3e5){x(e.keys),p(!0);return}}h(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);i.set(s,{keys:a,timestamp:Date.now()}),x(a),p(!0)}}catch(e){}finally{h(!1)}}},[s]);(0,r.useEffect)(()=>{s&&!g&&j(!0)},[s,j,g]),(0,r.useEffect)(()=>{let e=e=>{y.current&&!y.current.contains(e.target)&&o(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[d]);let f=e=>{o(!1),a(e)};return(0,t.jsxs)("div",{className:"relative ".concat(n),ref:y,children:[(0,t.jsxs)("button",{onClick:()=>{d||0!==m.length||g||j(!0),o(!d)},disabled:c,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(c?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":"")}),(0,t.jsx)(l.D,{className:"w-3 h-3 stroke-2"})]}),d&&(0,t.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),j(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,t.jsx)(l.E,{className:"w-3 h-3 ".concat(u?"animate-spin":"")})})]}),(0,t.jsxs)("button",{onClick:()=>f(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,t.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,t.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,t.jsxs)("button",{onClick:()=>f(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&g&&(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,t.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,t.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=i.get(s);return e&&Date.now()-e.timestamp<3e5?(0,t.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>r,CE:()=>l});var t=a(95155);function r(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function l(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},38050:(e,s,a)=>{a.d(s,{default:()=>n});var t=a(12115),r=a(35695),l=a(5777),i=a(44042);function n(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:n=!0}=e,c=(0,r.usePathname)(),d=(0,t.useRef)(""),o=(0,t.useRef)(0),{exportMetrics:m}=(0,i.D)("PerformanceTracker");return(0,t.useEffect)(()=>{if(!a)return;let e=d.current;e&&e!==c&&(l.zf.trackNavigation(e,c),performance.now(),o.current),d.current=c,o.current=performance.now()},[c,a]),(0,t.useEffect)(()=>{if(!n)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&l.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?l.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?l.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&l.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[n]),(0,t.useEffect)(()=>{let e;if(!s)return;let a=!1,t=0,r=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;t=Math.max(t,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),t>80&&("/"===c?(l.zf.schedulePrefetch("/pricing"),l.zf.schedulePrefetch("/features")):"/features"===c&&l.zf.schedulePrefetch("/auth/signup")),t=0},150)},i=performance.now(),n=()=>{performance.now()-i>1e4&&("/"===c?l.zf.schedulePrefetch("/auth/signup"):"/pricing"===c&&l.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",r,{passive:!0});let d=()=>{document.hidden&&n()};document.addEventListener("visibilitychange",d);let o=()=>{n()};return window.addEventListener("beforeunload",o),()=>{clearTimeout(e),window.removeEventListener("scroll",r),document.removeEventListener("visibilitychange",d),window.removeEventListener("beforeunload",o),n()}},[c,s,m]),(0,t.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,t=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),t.disconnect()}}},[]),null}},49487:(e,s,a)=>{a.d(s,{z:()=>H});var t=a(95155),r=a(12115),l=a(74338);let i=(0,r.forwardRef)((e,s)=>{let{className:a="",variant:r="default",size:i="default",loading:n=!1,icon:c,iconPosition:d="left",children:o,disabled:m,...x}=e,u={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},h=m||n;return(0,t.jsxs)("button",{ref:s,className:"".concat("inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[r]," ").concat({default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[i]," ").concat(a),disabled:h,...x,children:[n&&(0,t.jsx)(l.Ay,{size:"lg"===i?"md":"sm",className:"mr-2"}),!n&&c&&"left"===d&&(0,t.jsx)("span",{className:"".concat(u[i]," mr-2"),children:c}),o,!n&&c&&"right"===d&&(0,t.jsx)("span",{className:"".concat(u[i]," ml-2"),children:c})]})});i.displayName="Button";let n=(0,r.forwardRef)((e,s)=>{let{className:a="",variant:r="default",hover:l=!1,padding:i="md",children:n,...c}=e;return(0,t.jsx)("div",{ref:s,className:"".concat("rounded-xl transition-all duration-200"," ").concat({default:"card",glass:"glass",gradient:"gradient-surface border border-white/10"}[r]," ").concat({sm:"p-4",md:"p-6",lg:"p-8"}[i]," ").concat(l?"hover:shadow-md hover:-translate-y-1 cursor-pointer":""," ").concat(a),...c,children:n})});n.displayName="Card";let c=e=>{let{className:s="",title:a,subtitle:r,action:l,children:i,...n}=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6 ".concat(s),...n,children:[(0,t.jsxs)("div",{children:[a&&(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:a}),r&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:r}),i]}),l&&(0,t.jsx)("div",{children:l})]})},d=e=>{let{className:s="",children:a,...r}=e;return(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white ".concat(s),...r,children:a})},o=e=>{let{className:s="",children:a,...r}=e;return(0,t.jsx)("div",{className:"".concat(s),...r,children:a})};var m=a(75624);let x=(0,m.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function u(e){let{className:s,variant:a,...r}=e;return(0,t.jsx)("div",{className:"".concat(x({variant:a})," ").concat(s||""),...r})}var h=a(84168),g=a(56671),p=a(93295),y=a(574);function j(e){var s;let{apiKey:a,onEdit:r,onRevoke:l,onViewUsage:m}=e,x=async e=>{try{await navigator.clipboard.writeText(e),g.oR.success("API key copied to clipboard")}catch(e){g.oR.error("Failed to copy API key")}},h=a.expires_at&&new Date(a.expires_at)<new Date,j="active"===a.status&&!h;return(0,t.jsxs)(n,{className:"transition-all duration-200 hover:shadow-md ".concat(j?"":"opacity-75"),children:[(0,t.jsx)(c,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(d,{className:"text-lg font-semibold",children:a.key_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Configuration: ",a.custom_api_configs.name]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(u,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800 border-green-200";case"inactive":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"revoked":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(a.status),children:a.status}),h&&(0,t.jsx)(u,{className:"bg-red-100 text-red-800 border-red-200",children:"Expired"})]})]})}),(0,t.jsxs)(o,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"API Key (Masked)"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-600",children:[a.key_prefix,"_","*".repeat(28),(null==(s=a.masked_key)?void 0:s.slice(-4))||"xxxx"]}),(0,t.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>{var e;return x("".concat(a.key_prefix,"_").concat("*".repeat(28)).concat((null==(e=a.masked_key)?void 0:e.slice(-4))||"xxxx"))},className:"h-8 w-8 p-0",title:"Copy masked key (for reference only)",children:(0,t.jsx)(p.QR,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-600 bg-amber-50 p-2 rounded",children:[(0,t.jsx)("span",{children:"⚠️"}),(0,t.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Permissions"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.permissions.chat&&(0,t.jsx)(u,{variant:"secondary",className:"text-xs",children:"Chat Completions"}),a.permissions.streaming&&(0,t.jsx)(u,{variant:"secondary",className:"text-xs",children:"Streaming"}),a.permissions.all_models&&(0,t.jsx)(u,{variant:"secondary",className:"text-xs",children:"All Models"})]})]}),(a.allowed_ips.length>0||a.allowed_domains.length>0)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(p.ek,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs",children:[a.allowed_ips.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,t.jsx)("span",{children:a.allowed_ips.join(", ")})]}),a.allowed_domains.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)(p.qz,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,t.jsx)("span",{children:a.allowed_domains.join(", ")})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(p.Il,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Requests:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:a.total_requests.toLocaleString()})]}),a.last_used_at&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Last Used:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:(0,y.m)(new Date(a.last_used_at),{addSuffix:!0})})]})]})]}),a.expires_at&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(p.Vv,{className:"h-4 w-4"}),"Expiration"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("span",{className:"font-semibold ".concat(h?"text-red-600":"text-gray-900"),children:[new Date(a.expires_at).toLocaleDateString()," at"," ",new Date(a.expires_at).toLocaleTimeString()]}),!h&&(0,t.jsxs)("span",{className:"ml-2 text-gray-600",children:["(",(0,y.m)(new Date(a.expires_at),{addSuffix:!0}),")"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i,{variant:"outline",size:"sm",onClick:()=>m(a.id),className:"text-xs",children:[(0,t.jsx)(p.Il,{className:"h-3 w-3 mr-1"}),"View Usage"]}),j&&(0,t.jsxs)(i,{variant:"outline",size:"sm",onClick:()=>r(a),className:"text-xs",children:[(0,t.jsx)(p.wB,{className:"h-3 w-3 mr-1"}),"Edit"]})]}),"revoked"!==a.status&&(0,t.jsxs)(i,{variant:"destructive",size:"sm",onClick:()=>l(a.id),className:"text-xs",children:[(0,t.jsx)(p.TB,{className:"h-3 w-3 mr-1"}),"Revoke"]})]})]})]})}var f=a(90029),b=a(76288);let v=f.bL;f.l9;let N=f.ZL;f.bm;let w=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(f.hJ,{ref:s,className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ".concat(a||""),...r})});w.displayName=f.hJ.displayName;let k=r.forwardRef((e,s)=>{let{className:a,children:r,...l}=e;return(0,t.jsxs)(N,{children:[(0,t.jsx)(w,{}),(0,t.jsxs)(f.UC,{ref:s,className:"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ".concat(a||""),...l,children:[r,(0,t.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(b.X,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});k.displayName=f.UC.displayName;let _=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col space-y-1.5 text-center sm:text-left ".concat(s||""),...a})};_.displayName="DialogHeader";let C=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ".concat(s||""),...a})};C.displayName="DialogFooter";let A=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(f.hE,{ref:s,className:"text-lg font-semibold leading-none tracking-tight ".concat(a||""),...r})});A.displayName=f.hE.displayName;let P=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(f.VY,{ref:s,className:"text-sm text-gray-600 ".concat(a||""),...r})});P.displayName=f.VY.displayName;let S=(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:i,icon:n,iconPosition:c="left",id:d,...o}=e,m=d||"input-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("div",{className:"relative",children:[n&&"left"===c&&(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})}),(0,t.jsx)("input",{ref:s,id:m,className:"\n              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n              focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n              ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n              ").concat(n&&"left"===c?"pl-10":"","\n              ").concat(n&&"right"===c?"pr-10":"","\n              ").concat(a,"\n            "),...o}),n&&"right"===c&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})})]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),i&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:i})]})});S.displayName="Input",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:i,id:n,...c}=e,d=n||"textarea-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:d,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsx)("textarea",{ref:s,id:d,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200 resize-none\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...c}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),i&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:i})]})}).displayName="Textarea",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:i,options:n=[],children:c,id:d,...o}=e,m=d||"select-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("select",{ref:s,id:m,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...o,children:[n.map(e=>(0,t.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),c]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),i&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:i})]})}).displayName="Select";var R=a(40968);let L=(0,m.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),I=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(R.b,{ref:s,className:"".concat(L()," ").concat(a||""),...r})});I.displayName=R.b.displayName;let E=(0,m.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),z=r.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:"".concat(E({variant:r})," ").concat(a||""),...l})});z.displayName="Alert",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:"mb-1 font-medium leading-none tracking-tight ".concat(a||""),...r})}).displayName="AlertTitle";let D=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:"text-sm [&_p]:leading-relaxed ".concat(a||""),...r})});D.displayName="AlertDescription";var T=a(31547);function F(e){let{open:s,onOpenChange:a,onCreateApiKey:l,configName:n,creating:c,subscriptionTier:d}=e,[o,m]=(0,r.useState)("form"),[x,u]=(0,r.useState)(null),[h,p]=(0,r.useState)(!0),[y,j]=(0,r.useState)(!1),[f,b]=(0,r.useState)({key_name:"",expires_at:""}),N=async e=>{if(e.preventDefault(),!f.key_name.trim())return void g.oR.error("Please enter a name for your API key");try{let e=await l({key_name:f.key_name.trim(),expires_at:f.expires_at||void 0});u(e),m("success")}catch(e){}},w=async()=>{if(null==x?void 0:x.api_key)try{await navigator.clipboard.writeText(x.api_key),j(!0),g.oR.success("API key copied to clipboard"),setTimeout(()=>j(!1),2e3)}catch(e){g.oR.error("Failed to copy API key")}},R=()=>{"form"===o&&(m("form"),u(null),p(!0),b({key_name:"",expires_at:""}),a(!1))};return"success"===o&&x?(0,t.jsx)(v,{open:s,onOpenChange:()=>{},modal:!0,children:(0,t.jsxs)(k,{className:"max-w-lg",children:[(0,t.jsxs)(_,{className:"text-center space-y-3",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(T.Uz,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)(A,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,t.jsx)(P,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)(z,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(T.hc,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(D,{className:"text-red-800 font-medium",children:[(0,t.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(I,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:h?x.api_key:"".concat(x.key_prefix,"_").concat("*".repeat(28)).concat(x.api_key.slice(-4))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i,{variant:"ghost",size:"sm",onClick:()=>p(!h),className:"h-8 w-8 p-0",title:h?"Hide key":"Show key",children:h?(0,t.jsx)(T.X_,{className:"h-4 w-4"}):(0,t.jsx)(T.kU,{className:"h-4 w-4"})}),(0,t.jsx)(i,{variant:"ghost",size:"sm",onClick:w,className:"h-8 w-8 p-0 ".concat(y?"text-green-600":""),title:"Copy to clipboard",children:y?(0,t.jsx)("span",{className:"text-xs",children:"✓"}):(0,t.jsx)(T.QR,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(i,{onClick:w,variant:"outline",className:"w-full",disabled:y,children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T.QR,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(I,{className:"text-gray-600",children:"Key Name"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:x.key_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(I,{className:"text-gray-600",children:"Created"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:new Date(x.created_at).toLocaleString()})]})]})]}),(0,t.jsx)(C,{className:"pt-6",children:(0,t.jsx)(i,{onClick:()=>{m("form"),u(null),p(!0),j(!1),b({key_name:"",expires_at:""}),a(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,t.jsx)(v,{open:s,onOpenChange:R,children:(0,t.jsxs)(k,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(_,{children:[(0,t.jsxs)(A,{className:"flex items-center gap-2",children:[(0,t.jsx)(T.Uz,{className:"h-5 w-5"}),"Create API Key"]}),(0,t.jsxs)(P,{children:["Create a new API key for programmatic access to ",n]})]}),(0,t.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{htmlFor:"key_name",children:"API Key Name *"}),(0,t.jsx)(S,{id:"key_name",value:f.key_name,onChange:e=>b(s=>({...s,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,t.jsx)(S,{id:"expires_at",type:"datetime-local",value:f.expires_at,onChange:e=>b(s=>({...s,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,t.jsxs)(C,{children:[(0,t.jsx)(i,{type:"button",variant:"outline",onClick:R,children:"Cancel"}),(0,t.jsx)(i,{type:"submit",disabled:c,children:c?"Creating...":"Create API Key"})]})]})]})})}var K=a(77484),M=a(4e3);let O=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(K.bL,{ref:s,className:"peer h-4 w-4 shrink-0 rounded-sm border border-gray-300 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-orange-600 data-[state=checked]:text-white data-[state=checked]:border-orange-600 ".concat(a||""),...r,children:(0,t.jsx)(K.C1,{className:"flex items-center justify-center text-current",children:(0,t.jsx)(M.J,{className:"h-4 w-4"})})})});O.displayName=K.bL.displayName;var U=a(87489);let q=r.forwardRef((e,s)=>{let{className:a,orientation:r="horizontal",decorative:l=!0,...i}=e;return(0,t.jsx)(U.b,{ref:s,decorative:l,orientation:r,className:"shrink-0 bg-gray-200 ".concat("horizontal"===r?"h-[1px] w-full":"h-full w-[1px]"," ").concat(a||""),...i})});q.displayName=U.b.displayName;var B=a(78507);function W(e){let{open:s,onOpenChange:a,apiKey:l,onUpdateApiKey:n}=e,[c,d]=(0,r.useState)(!1),[o,m]=(0,r.useState)({key_name:"",permissions:{chat:!0,streaming:!0,all_models:!0},allowed_ips:[],allowed_domains:[],status:"active",expires_at:""}),[x,h]=(0,r.useState)(""),[p,y]=(0,r.useState)("");(0,r.useEffect)(()=>{l&&m({key_name:l.key_name||"",permissions:l.permissions||{chat:!0,streaming:!0,all_models:!0},allowed_ips:l.allowed_ips||[],allowed_domains:l.allowed_domains||[],status:l.status||"active",expires_at:l.expires_at?new Date(l.expires_at).toISOString().slice(0,16):""})},[l]);let j=async e=>{if(e.preventDefault(),!o.key_name.trim())return void g.oR.error("Please enter a name for your API key");try{d(!0),await n({...o,key_name:o.key_name.trim(),expires_at:o.expires_at||null})}catch(e){}finally{d(!1)}},f=()=>{x.trim()&&!o.allowed_ips.includes(x.trim())&&(m(e=>({...e,allowed_ips:[...e.allowed_ips,x.trim()]})),h(""))},b=e=>{m(s=>({...s,allowed_ips:s.allowed_ips.filter(s=>s!==e)}))},N=()=>{p.trim()&&!o.allowed_domains.includes(p.trim())&&(m(e=>({...e,allowed_domains:[...e.allowed_domains,p.trim()]})),y(""))},w=e=>{m(s=>({...s,allowed_domains:s.allowed_domains.filter(s=>s!==e)}))};return(0,t.jsx)(v,{open:s,onOpenChange:a,children:(0,t.jsxs)(k,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(_,{children:[(0,t.jsxs)(A,{className:"flex items-center gap-2",children:[(0,t.jsx)(B.wB,{className:"h-5 w-5"}),"Edit API Key"]}),(0,t.jsx)(P,{children:"Update the settings for your API key"})]}),(0,t.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{htmlFor:"key_name",children:"API Key Name *"}),(0,t.jsx)(S,{id:"key_name",value:o.key_name,onChange:e=>m(s=>({...s,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{htmlFor:"status",children:"Status"}),(0,t.jsxs)("select",{id:"status",value:o.status,onChange:e=>m(s=>({...s,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"active",children:"Active"}),(0,t.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,t.jsx)(q,{}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(B.ek,{className:"h-4 w-4"}),(0,t.jsx)(I,{className:"text-base font-semibold",children:"Permissions"})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(O,{id:"chat",checked:o.permissions.chat,onCheckedChange:e=>m(s=>({...s,permissions:{...s.permissions,chat:!!e}}))}),(0,t.jsx)(I,{htmlFor:"chat",className:"text-sm",children:"Chat Completions"}),(0,t.jsx)(u,{variant:"secondary",className:"text-xs",children:"Required"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(O,{id:"streaming",checked:o.permissions.streaming,onCheckedChange:e=>m(s=>({...s,permissions:{...s.permissions,streaming:!!e}}))}),(0,t.jsx)(I,{htmlFor:"streaming",className:"text-sm",children:"Streaming Responses"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(O,{id:"all_models",checked:o.permissions.all_models,onCheckedChange:e=>m(s=>({...s,permissions:{...s.permissions,all_models:!!e}}))}),(0,t.jsx)(I,{htmlFor:"all_models",className:"text-sm",children:"Access to All Models"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(B.ek,{className:"h-4 w-4"}),(0,t.jsx)(I,{className:"text-base font-semibold",children:"Security Restrictions"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{children:"Allowed IP Addresses"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(S,{value:x,onChange:e=>h(e.target.value),placeholder:"e.g., *********** or 10.0.0.0/8",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),f())}),(0,t.jsx)(i,{type:"button",onClick:f,size:"sm",children:(0,t.jsx)(B.FW,{className:"h-4 w-4"})})]}),o.allowed_ips.length>0&&(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:o.allowed_ips.map(e=>(0,t.jsxs)(u,{variant:"secondary",className:"text-xs",children:[e,(0,t.jsx)(i,{type:"button",variant:"ghost",size:"sm",onClick:()=>b(e),className:"ml-1 h-3 w-3 p-0",children:(0,t.jsx)(B.X,{className:"h-2 w-2"})})]},e))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{children:"Allowed Domains (CORS)"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(S,{value:p,onChange:e=>y(e.target.value),placeholder:"e.g., example.com or *.example.com",onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),N())}),(0,t.jsx)(i,{type:"button",onClick:N,size:"sm",children:(0,t.jsx)(B.FW,{className:"h-4 w-4"})})]}),o.allowed_domains.length>0&&(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:o.allowed_domains.map(e=>(0,t.jsxs)(u,{variant:"secondary",className:"text-xs",children:[(0,t.jsx)(B.qz,{className:"h-3 w-3 mr-1"}),e,(0,t.jsx)(i,{type:"button",variant:"ghost",size:"sm",onClick:()=>w(e),className:"ml-1 h-3 w-3 p-0",children:(0,t.jsx)(B.X,{className:"h-2 w-2"})})]},e))})]})]}),(0,t.jsx)(q,{}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,t.jsx)(S,{id:"expires_at",type:"datetime-local",value:o.expires_at,onChange:e=>m(s=>({...s,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16)})]})}),(0,t.jsxs)(C,{children:[(0,t.jsx)(i,{type:"button",variant:"outline",onClick:()=>a(!1),children:"Cancel"}),(0,t.jsx)(i,{type:"submit",disabled:c,children:c?"Updating...":"Update API Key"})]})]})]})})}var V=a(86558);function Y(e){let{open:s,onOpenChange:a,apiKey:l}=e,[m,x]=(0,r.useState)(!0),[h,g]=(0,r.useState)(null),[p,j]=(0,r.useState)([]),[f,b]=(0,r.useState)("24h"),N=async()=>{if(null==l?void 0:l.id)try{x(!0);let e=await fetch("/api/user-api-keys/".concat(l.id,"/usage/stats?range=").concat(f));if(e.ok){let s=await e.json();g(s)}let s=await fetch("/api/user-api-keys/".concat(l.id,"/usage/logs?limit=50&range=").concat(f));if(s.ok){let e=await s.json();j(e.usage_logs||[])}}catch(e){}finally{x(!1)}};(0,r.useEffect)(()=>{s&&(null==l?void 0:l.id)&&N()},[s,null==l?void 0:l.id,f]);let w=e=>e>=200&&e<300?"bg-green-100 text-green-800 border-green-200":e>=400&&e<500?"bg-yellow-100 text-yellow-800 border-yellow-200":e>=500?"bg-red-100 text-red-800 border-red-200":"bg-gray-100 text-gray-800 border-gray-200",C=async()=>{try{let e=await fetch("/api/user-api-keys/".concat(l.id,"/usage/export?range=").concat(f));if(e.ok){let s=await e.blob(),a=window.URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download="api-key-usage-".concat(l.key_name,"-").concat(f,".csv"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(a),document.body.removeChild(t)}}catch(e){}};return m?(0,t.jsx)(v,{open:s,onOpenChange:a,children:(0,t.jsxs)(k,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(_,{children:(0,t.jsxs)(A,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.Il,{className:"h-5 w-5"}),"API Key Usage"]})}),(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(V.e9,{className:"h-6 w-6 animate-spin mr-2"}),"Loading usage data..."]})]})}):(0,t.jsx)(v,{open:s,onOpenChange:a,children:(0,t.jsxs)(k,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(_,{children:[(0,t.jsxs)(A,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.Il,{className:"h-5 w-5"}),"API Key Usage: ",l.key_name]}),(0,t.jsx)(P,{children:"Usage statistics and logs for your API key"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(i,{variant:"24h"===f?"default":"outline",size:"sm",onClick:()=>b("24h"),children:"Last 24 Hours"}),(0,t.jsx)(i,{variant:"7d"===f?"default":"outline",size:"sm",onClick:()=>b("7d"),children:"Last 7 Days"}),(0,t.jsx)(i,{variant:"30d"===f?"default":"outline",size:"sm",onClick:()=>b("30d"),children:"Last 30 Days"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i,{variant:"outline",size:"sm",onClick:N,children:[(0,t.jsx)(V.e9,{className:"h-4 w-4 mr-1"}),"Refresh"]}),(0,t.jsxs)(i,{variant:"outline",size:"sm",onClick:C,children:[(0,t.jsx)(V.f5,{className:"h-4 w-4 mr-1"}),"Export"]})]})]}),h&&(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(n,{children:[(0,t.jsx)(c,{className:"pb-2",children:(0,t.jsx)(d,{className:"text-sm font-medium text-gray-600",children:"Total Requests"})}),(0,t.jsxs)(o,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:h.total_requests.toLocaleString()}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:[h.requests_today," today"]})]})]}),(0,t.jsxs)(n,{children:[(0,t.jsx)(c,{className:"pb-2",children:(0,t.jsx)(d,{className:"text-sm font-medium text-gray-600",children:"Success Rate"})}),(0,t.jsxs)(o,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[h.success_rate.toFixed(1),"%"]}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"HTTP 2xx responses"})]})]}),(0,t.jsxs)(n,{children:[(0,t.jsx)(c,{className:"pb-2",children:(0,t.jsx)(d,{className:"text-sm font-medium text-gray-600",children:"Avg Response Time"})}),(0,t.jsxs)(o,{children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[h.avg_response_time,"ms"]}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Average latency"})]})]}),(0,t.jsxs)(n,{children:[(0,t.jsx)(c,{className:"pb-2",children:(0,t.jsx)(d,{className:"text-sm font-medium text-gray-600",children:"Total Tokens"})}),(0,t.jsxs)(o,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:h.total_tokens.toLocaleString()}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:["$",h.estimated_cost.toFixed(4)," estimated"]})]})]})]}),(0,t.jsxs)(n,{children:[(0,t.jsx)(c,{children:(0,t.jsxs)(d,{className:"text-lg flex items-center gap-2",children:[(0,t.jsx)(V.Vv,{className:"h-5 w-5"}),"Recent Requests"]})}),(0,t.jsx)(o,{children:0===p.length?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(V.RI,{className:"h-8 w-8 mx-auto mb-2"}),"No usage logs found for the selected time range"]}):(0,t.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:p.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(u,{className:w(e.status_code),children:e.status_code}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:[e.http_method," ",e.endpoint]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:[(0,y.m)(new Date(e.request_timestamp),{addSuffix:!0}),e.ip_address&&" • ".concat(e.ip_address)]})]})]}),(0,t.jsxs)("div",{className:"text-right text-xs text-gray-600",children:[e.model_used&&(0,t.jsxs)("div",{children:["Model: ",e.model_used]}),e.response_time_ms&&(0,t.jsxs)("div",{children:[e.response_time_ms,"ms"]}),e.tokens_prompt&&e.tokens_completion&&(0,t.jsxs)("div",{children:[(e.tokens_prompt+e.tokens_completion).toLocaleString()," tokens"]})]})]},e.id))})})]})]})]})})}function H(e){let{configId:s,configName:a}=e,[l,c]=(0,r.useState)([]),[d,m]=(0,r.useState)(!0),[x,p]=(0,r.useState)(!1),[y,f]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!1),[k,_]=(0,r.useState)(null),[C,A]=(0,r.useState)(null),P=async()=>{try{m(!0);let e=await fetch("/api/user-api-keys?config_id=".concat(s));if(!e.ok)throw Error("Failed to fetch API keys");let a=await e.json();c(a.api_keys||[])}catch(e){g.oR.error("Failed to load API keys")}finally{m(!1)}},S=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let s=await e.json(),a=await fetch("/api/user/subscription-tier"),t=a.ok?await a.json():null,r=(null==t?void 0:t.tier)||s.subscription_tier||"starter",i={starter:5,professional:25,enterprise:100};A({tier:r,keyLimit:i[r]||i.starter,currentCount:l.length})}}catch(e){A({tier:"starter",keyLimit:2,currentCount:l.length})}};(0,r.useEffect)(()=>{P()},[s]),(0,r.useEffect)(()=>{l.length>=0&&S()},[l]);let R=async e=>{try{p(!0);let a=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,custom_api_config_id:s})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to create API key")}let t=await a.json();return g.oR.success("API key created successfully!"),t}catch(e){throw g.oR.error(e.message||"Failed to create API key"),e}finally{p(!1)}},L=async(e,s)=>{try{let a=await fetch("/api/user-api-keys/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to update API key")}g.oR.success("API key updated successfully"),await P(),v(!1),_(null)}catch(e){g.oR.error(e.message||"Failed to update API key")}},I=async e=>{if(confirm("Are you sure you want to revoke this API key? This action cannot be undone."))try{let s=await fetch("/api/user-api-keys/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to revoke API key")}g.oR.success("API key revoked successfully"),await P()}catch(e){g.oR.error(e.message||"Failed to revoke API key")}},E=e=>{_(l.find(s=>s.id===e)),w(!0)},T=!C||C.currentCount<C.keyLimit;return d?(0,t.jsx)(n,{children:(0,t.jsxs)(o,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(h.e9,{className:"h-6 w-6 animate-spin mr-2"}),"Loading API keys..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,t.jsx)(h.Uz,{className:"h-6 w-6"}),"API Keys"]}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:["Generate API keys for programmatic access to ",a]})]}),(0,t.jsxs)(i,{onClick:()=>f(!0),disabled:!T,className:"flex items-center gap-2",children:[(0,t.jsx)(h.FW,{className:"h-4 w-4"}),"Create API Key"]})]}),C&&(0,t.jsxs)(z,{children:[(0,t.jsx)(h.RI,{className:"h-4 w-4"}),(0,t.jsxs)(D,{children:["You are on the ",(0,t.jsx)(u,{variant:"secondary",children:C.tier})," plan. You have used ",C.currentCount," of ",C.keyLimit," API keys.",!T&&(0,t.jsx)("span",{className:"text-red-600 ml-2",children:"Upgrade your plan to create more API keys."})]})]}),0===l.length?(0,t.jsx)(n,{children:(0,t.jsxs)(o,{className:"text-center py-8",children:[(0,t.jsx)(h.Uz,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No API Keys"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),(0,t.jsxs)(i,{onClick:()=>f(!0),disabled:!T,children:[(0,t.jsx)(h.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]})]})}):(0,t.jsx)("div",{className:"grid gap-4",children:l.map(e=>(0,t.jsx)(j,{apiKey:e,onEdit:e=>{_(e),v(!0)},onRevoke:I,onViewUsage:E},e.id))}),(0,t.jsx)(F,{open:y,onOpenChange:e=>{f(e),e||P()},onCreateApiKey:R,configName:a,creating:x,subscriptionTier:(null==C?void 0:C.tier)||"starter"}),k&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(W,{open:b,onOpenChange:v,apiKey:k,onUpdateApiKey:e=>L(k.id,e)}),(0,t.jsx)(Y,{open:N,onOpenChange:w,apiKey:k})]})]})}},50956:(e,s,a)=>{a.d(s,{A:()=>c});var t=a(95155),r=a(12115),l=a(6874),i=a.n(l),n=a(35695);function c(e){let{href:s,children:a,className:l="",prefetch:c=!0}=e,d=(0,n.useRouter)();return(0,t.jsx)(i(),{href:s,className:l,onClick:e=>{e.preventDefault(),(0,r.startTransition)(()=>{d.push(s)})},prefetch:c,children:a})}},69903:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(12115),l=a(35695),i=a(99323);let n=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,t.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"py-20",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,t.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),o=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),x=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function u(e){let s,{targetRoute:a,children:u}=e,[h,g]=(0,r.useState)(!0),[p,y]=(0,r.useState)(!1),j=(0,l.usePathname)(),f=(0,r.useRef)(),{isPageCached:b}=(0,i.bu)()||{isPageCached:()=>!1};return((0,r.useEffect)(()=>(j===a&&(f.current=setTimeout(()=>{y(!0),setTimeout(()=>g(!1),100)},b(a)?50:200)),()=>{f.current&&clearTimeout(f.current)}),[j,a,b]),(0,r.useEffect)(()=>{g(!0),y(!1)},[a]),j!==a&&h||j===a&&h&&!p)?(0,t.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,t.jsx)(n,{}):s.startsWith("/pricing")?(0,t.jsx)(c,{}):s.startsWith("/features")?(0,t.jsx)(d,{}):s.startsWith("/auth/")?(0,t.jsx)(o,{}):s.startsWith("/playground")?(0,t.jsx)(m,{}):(0,t.jsx)(x,{})}):(0,t.jsx)("div",{className:"transition-opacity duration-300 ".concat(p?"opacity-100":"opacity-0"),children:u})}},71848:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(23405);let l=e=>{let{label:s,value:a}=e;return(0,t.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:s}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},i=e=>{let s,{title:a,data:r}=e;if(null==r)s="N/A";else if("string"==typeof r)s=r;else try{s=JSON.stringify(r,null,2)}catch(e){s="Invalid JSON data"}return(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function n(e){var s;let{log:a,onClose:n,apiConfigNameMap:c}=e;if(!a)return null;let d=a.custom_api_config_id?c[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:n,children:(0,t.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,t.jsx)("button",{onClick:n,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(r.f,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,t.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,t.jsx)(l,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,t.jsx)(l,{label:"API Model Used",value:d}),(0,t.jsx)(l,{label:"Role Requested",value:a.role_requested}),(0,t.jsx)(l,{label:"Role Used",value:a.role_used}),(0,t.jsx)(l,{label:"Status",value:null===(s=a.status_code)?(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,t.jsx)(l,{label:"LLM Provider",value:a.llm_provider_name}),(0,t.jsx)(l,{label:"LLM Model Name",value:(()=>{var e,s;if((null==(e=a.role_used)?void 0:e.includes("RouKey_Multi Roles_"))&&(null==(s=a.response_payload_summary)?void 0:s.models_used)){let e=a.response_payload_summary.models_used;return e.length<=3?e.join(", "):"".concat(e.slice(0,3).join(", "),"...")}return a.llm_model_name})()}),(0,t.jsx)(l,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,t.jsx)(l,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,t.jsx)(l,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,t.jsx)(l,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,t.jsx)(l,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,t.jsx)(l,{label:"User ID",value:a.user_id}),a.error_message&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{label:"Error Message",value:a.error_message}),(0,t.jsx)(l,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,t.jsx)(l,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,t.jsx)(i,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,t.jsx)(i,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,t.jsx)(i,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,t.jsx)("button",{onClick:n,className:"btn-secondary",children:"Close"})})]})})}},74338:(e,s,a)=>{a.d(s,{Ay:()=>r,B0:()=>l,F6:()=>i});var t=a(95155);function r(e){let{size:s="md",className:a=""}=e;return(0,t.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ".concat({sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[s]," ").concat(a)})}function l(e){let{className:s=""}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function i(e){let{rows:s=5,columns:a=4}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:s}).map((e,s)=>(0,t.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},s))]})})}},78817:(e,s,a)=>{a.d(s,{A:()=>c});var t=a(95155);a(12115);var r=a(89732),l=a(99323),i=a(95565);let n={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:r.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:r.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:r.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:r.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:r.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:r.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:r.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function c(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,l.bu)()||{clearNavigation:()=>{}};if(!(s?n[s]:null))return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,t.jsx)(r.cu,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let c=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,t.jsx)(r.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,t.jsx)(i.O2,{});case"/my-models":return(0,t.jsx)(i.MyModelsSkeleton,{});case"/playground":return(0,t.jsx)(c,{});case"/routing-setup":return(0,t.jsx)(i.RoutingSetupSkeleton,{});case"/logs":return(0,t.jsx)(d,{});case"/training":return(0,t.jsx)(i.vD,{});case"/analytics":return(0,t.jsx)(i.AnalyticsSkeleton,{})}})()]})}},80377:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(12115),l=a(38152);function i(e){let{isOpen:s,onClose:a,onConfirm:i,title:n,message:c,confirmText:d="Delete",cancelText:o="Cancel",type:m="danger",isLoading:x=!1}=e;(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!x&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,x,a]);let u=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:l.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.Pi}}})(),h=u.icon;return s?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:x?void 0:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,t.jsx)("div",{className:"relative px-6 pt-6",children:(0,t.jsx)("button",{onClick:a,disabled:x,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)(l.fK,{className:"h-5 w-5"})})}),(0,t.jsxs)("div",{className:"px-6 pb-6",children:[(0,t.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,t.jsx)("div",{className:"".concat(u.iconBg," rounded-full p-3"),children:(0,t.jsx)(h,{className:"h-8 w-8 ".concat(u.iconColor)})})}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:n}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:c}),(0,t.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,t.jsx)("button",{type:"button",onClick:a,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:o}),(0,t.jsx)("button",{type:"button",onClick:i,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(u.confirmButton),children:x?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},95060:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(95155),r=a(6874),l=a.n(r),i=a(66766),n=a(35695),c=a(12115),d=a(8652),o=a(14097),m=a(22261),x=a(99323),u=a(37843),h=a(24403),g=a(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:d.fA,iconSolid:o.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:d.RY,iconSolid:o.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:d.cu,iconSolid:o.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:d.sR,iconSolid:o.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:d.AQ,iconSolid:o.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:d.tl,iconSolid:o.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:d.r9,iconSolid:o.r9,description:"Advanced insights"}];function y(){let e=(0,n.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:r,setHovered:d}=(0,m.c)(),{navigateOptimistically:o}=(0,x.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:y}=(0,u.C)(),{prefetchWhenIdle:j}=(0,u.e)(),{prefetchChatHistory:f}=(0,h.l2)(),{predictions:b,isLearning:v}=(0,g.x)(),N=(0,g.G)();(0,c.useEffect)(()=>{let s=p.map(e=>e.href),a=b.slice(0,2),t=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return j([...a,...t,...s.filter(s=>s!==e&&!a.includes(s)&&!t.includes(s)),"/playground","/logs"].slice(0,6))},[e,j,b,N,v]);let w=!s||a;return(0,t.jsx)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!r&&d(!0),onMouseLeave:()=>!r&&d(!1),children:(0,t.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,t.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,t.jsx)(i.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,t.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,t.jsx)("nav",{className:"space-y-2",children:p.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),r=a?s.iconSolid:s.icon,i=b.includes(s.href),n=N.find(e=>e.route===s.href),c="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){y(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&f(e)}}}:y(s.href,50);return(0,t.jsx)(l(),{href:s.href,onClick:e=>{e.preventDefault(),o(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...c,children:(0,t.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,t.jsx)(r,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),i&&!a&&(0,t.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,t.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:s.label}),n&&!a&&(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===n.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===n.priority?"!":"\xb7"})]}),(0,t.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:n?n.reason:s.description})]})]})},s.href)})})]})})})}},95494:(e,s,a)=>{a.d(s,{A:()=>x});var t=a(95155),r=a(6874),l=a.n(r),i=a(12115),n=a(41045),c=a(22261),d=a(35695),o=a(83298),m=a(52643);function x(){var e,s,a,r,x,u,h;let{isCollapsed:g,isHovered:p,toggleSidebar:y}=(0,c.c)(),j=(0,d.usePathname)(),{user:f,subscriptionStatus:b}=(0,o.R)(),[v,N]=(0,i.useState)(!1),[w,k]=(0,i.useState)(!1),_=(0,m.u)();(0,i.useEffect)(()=>{let e=()=>{k(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let C=(null==f||null==(e=f.user_metadata)?void 0:e.first_name)||(null==f||null==(a=f.user_metadata)||null==(s=a.full_name)?void 0:s.split(" ")[0])||"User",A=C.charAt(0).toUpperCase()+((null==f||null==(u=f.user_metadata)||null==(x=u.last_name)||null==(r=x.charAt(0))?void 0:r.toUpperCase())||(null==(h=C.charAt(1))?void 0:h.toUpperCase())||"U"),P=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(j),S=(null==b?void 0:b.hasActiveSubscription)?(null==b?void 0:b.tier)==="starter"?"Starter Plan":(null==b?void 0:b.tier)==="professional"?"Professional Plan":(null==b?void 0:b.tier)==="enterprise"?"Enterprise Plan":"Starter Plan":"Starter Plan",R=async()=>{try{await _.auth.signOut(),window.location.href="/auth/signin"}catch(e){}};return(0,t.jsx)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:(0,t.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(w&&(!g||p)?"max-w-7xl mx-auto":w?"max-w-none":"max-w-7xl mx-auto"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:y,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,t.jsx)(n.tK,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)("span",{children:P.title}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:P.subtitle})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsx)("div",{className:"hidden xl:block",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",placeholder:"Search...",className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})}),(0,t.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,t.jsx)(n.XF,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,t.jsxs)("div",{className:"hidden sm:block relative",children:[(0,t.jsxs)("button",{onClick:()=>N(!v),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)(n.Vy,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)(n.D3,{className:"h-3 w-3 text-gray-600 transition-transform duration-200 ".concat(v?"rotate-180":"")})]}),v&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>N(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,t.jsxs)(l(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,t.jsx)(n.Vy,{className:"h-4 w-4 mr-3 text-gray-500"}),"Settings"]}),(0,t.jsxs)(l(),{href:"/dashboard/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>N(!1),children:[(0,t.jsx)(n.rM,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing"]}),(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("button",{onClick:R,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,t.jsx)(n.Rz,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:A})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:C}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:S})]})]})]})]})})})}},96364:(e,s,a)=>{a.d(s,{V:()=>l});var t=a(95155);a(12115);var r=a(82880);let l=e=>{let{senderName:s,roleId:a}=e,l=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(a),i=!a||"moderator"===a;return(0,t.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(l," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,t.jsx)(r.Y,{className:"w-4 h-4"}):(0,t.jsx)(r.B,{className:"w-4 h-4"}))(a)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(i?"text-blue-700":"text-gray-700"),children:s}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(s)})]}),(0,t.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(i?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})}}}]);